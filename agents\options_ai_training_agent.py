#!/usr/bin/env python3
"""
Options AI Training Agent - ML Model Training for Options Trading

Features:
🧠 1. Multi-Target Prediction Models
- Options price prediction
- Implied volatility forecasting
- Greeks prediction
- Strategy performance ranking

📊 2. Advanced Feature Engineering
- Options-specific features (Greeks, IV, time decay)
- Market microstructure features
- Volatility surface features
- Options flow indicators

⚡ 3. Model Ensemble
- LightGBM for tabular data (primary)
- PyTorch TabNet for deep learning
- XGBoost for ensemble
- Custom options pricing models

🎯 4. Performance Optimization
- GPU acceleration for training
- Hyperparameter optimization with Optuna
- Cross-validation for options data
- Model interpretability with SHAP
"""

import asyncio
import logging
import polars as pl
import pyarrow as pa
import pyarrow.compute as pc
import numpy as np
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
import joblib
import json
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

# ML libraries
import lightgbm as lgb
import optuna
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import mean_squared_error, mean_absolute_error
import shap

# Deep learning
try:
    import torch
    import torch.nn as nn
    from pytorch_tabnet.tab_model import TabNetRegressor
except ImportError:
    logger.warning("PyTorch/TabNet not installed. Using LightGBM only.")

logger = logging.getLogger(__name__)

class OptionsAITrainingAgent:
    """
    Options AI Training Agent for ML model development
    
    Handles:
    - Options price prediction models
    - Strategy performance prediction
    - Risk assessment models
    - Model ensemble creation
    """
    
    def __init__(self, config_path: str = "config/options_ai_training_config.yaml"):
        """Initialize Options AI Training Agent"""
        self.config_path = config_path
        self.config = None
        self.is_running = False
        
        # Data paths
        self.data_path = Path("data")
        self.features_path = self.data_path / "features"
        self.models_path = self.data_path / "models"
        self.backtest_path = self.data_path / "backtest"
        
        # Create directories
        self.models_path.mkdir(parents=True, exist_ok=True)
        
        # Model storage
        self.trained_models = {}
        
        logger.info("[INIT] Options AI Training Agent initialized")
    
    async def initialize(self):
        """Initialize the agent"""
        try:
            await self._load_config()
            logger.info("[SUCCESS] Options AI Training Agent initialized successfully")
            return True
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize agent: {e}")
            return False
    
    async def _load_config(self):
        """Load configuration"""
        self.config = {
            'target_variables': ['option_price', 'implied_vol', 'strategy_return', 'annualized_return', 'sharpe_ratio'],
            'feature_columns': [
                # Greeks features
                'delta', 'gamma', 'theta', 'vega', 'iv_rank',
                # Index reference features
                'index_price', 'index_return_1min', 'index_return_3min', 'index_return_5min', 'index_return_15min',
                'index_volatility', 'index_volatility_10', 'index_volatility_50',
                'index_momentum_10', 'index_momentum_20', 'index_above_sma20', 'index_above_sma50',
                # Options-specific features
                'moneyness', 'moneyness_deviation', 'distance_from_atm', 'is_itm', 'time_to_expiry',
                # Combined features
                'delta_exposure', 'delta_pnl_1min', 'gamma_risk', 'vega_exposure', 'theta_decay_rate',
                # PE/CE analysis features
                'is_pe', 'is_ce', 'direction_alignment',
                # Technical features (timeframe-dependent)
                'rsi', 'sma_20', 'ema_20', 'volume_ratio', 'momentum'
            ],
            'model_types': ['lightgbm', 'tabnet'],
            'cv_folds': 3,  # Reduced from 5 to 3 for faster training
            'optuna_trials': 10,  # Reduced from 50 to 10 for much faster training
            'optuna_timeout': 60,  # Add timeout per optimization (60 seconds)
            'use_index_reference': True,
            'pe_ce_filter': True,
            'timeframes': ['1min', '3min', '5min', '15min'],
            'train_ensemble': True,
            'parallel_training': True,  # Enable parallel training
            'max_workers': 4  # Maximum parallel workers
        }
    
    async def start(self, **kwargs) -> bool:
        """Start the multi-timeframe AI training agent"""
        try:
            logger.info("[START] Starting Options AI Training Agent...")

            # Load multi-timeframe training data
            training_data = await self._load_training_data()
            if training_data is None:
                return False

            # Load and integrate index reference data
            if self.config['use_index_reference']:
                training_data = await self._integrate_index_reference_data(training_data)

            # Create target variables from available data
            training_data = await self._create_target_variables(training_data)

            # Store timeframe datasets for individual training
            self.timeframe_datasets = {}

            # Train models for each timeframe and target combination
            timeframes = ['1min', '3min', '5min', '15min']

            # Prepare timeframe datasets first
            for timeframe in timeframes:
                logger.info(f"[TRAINING] Preparing {timeframe} timeframe data...")

                # Get timeframe-specific data
                timeframe_data = None

                # First try to filter from combined data
                if 'timeframe' in training_data.columns:
                    timeframe_data = training_data.filter(pl.col('timeframe') == timeframe)

                # If no data found, try to load timeframe data separately
                if timeframe_data is None or timeframe_data.height == 0:
                    timeframe_data = await self._load_and_process_timeframe_data(timeframe, ['NIFTY', 'BANKNIFTY'])

                if timeframe_data is None or timeframe_data.height == 0:
                    logger.warning(f"[WARNING] No data for {timeframe} timeframe")
                    continue

                # Store for ensemble training
                self.timeframe_datasets[timeframe] = timeframe_data

            # Train models in parallel if enabled
            if self.config.get('parallel_training', False):
                await self._train_models_parallel()
            else:
                # Sequential training (fallback)
                for timeframe in timeframes:
                    if timeframe not in self.timeframe_datasets:
                        continue
                    timeframe_data = self.timeframe_datasets[timeframe]
                    for target in self.config['target_variables']:
                        await self._train_timeframe_target_models(timeframe_data, target, timeframe)

            # Train ensemble models combining all timeframes
            await self._train_ensemble_models(training_data)

            # Save all models
            await self._save_models()

            logger.info("[SUCCESS] Multi-timeframe AI training completed")
            return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to start agent: {e}")
            return False

    async def _train_models_parallel(self):
        """Train models in parallel for faster execution"""
        try:
            logger.info("[PARALLEL] Starting parallel model training...")

            # Create training tasks
            training_tasks = []
            for timeframe, timeframe_data in self.timeframe_datasets.items():
                for target in self.config['target_variables']:
                    task = {
                        'timeframe': timeframe,
                        'target': target,
                        'data': timeframe_data
                    }
                    training_tasks.append(task)

            logger.info(f"[PARALLEL] Created {len(training_tasks)} training tasks")

            # Execute training tasks in parallel batches
            max_workers = self.config.get('max_workers', 4)
            batch_size = max_workers

            for i in range(0, len(training_tasks), batch_size):
                batch = training_tasks[i:i + batch_size]
                logger.info(f"[PARALLEL] Processing batch {i//batch_size + 1}/{(len(training_tasks) + batch_size - 1)//batch_size}")

                # Create coroutines for this batch
                batch_coroutines = []
                for task in batch:
                    coroutine = self._train_timeframe_target_models(
                        task['data'], task['target'], task['timeframe']
                    )
                    batch_coroutines.append(coroutine)

                # Execute batch in parallel
                await asyncio.gather(*batch_coroutines, return_exceptions=True)

            logger.info("[PARALLEL] Parallel training completed")

        except Exception as e:
            logger.error(f"[ERROR] Parallel training failed: {e}")
            # Fallback to sequential training
            logger.info("[FALLBACK] Switching to sequential training...")
            for timeframe, timeframe_data in self.timeframe_datasets.items():
                for target in self.config['target_variables']:
                    await self._train_timeframe_target_models(timeframe_data, target, timeframe)
    
    async def _load_training_data(self) -> Optional[pl.DataFrame]:
        """Load multi-timeframe training data with PE/CE filtering"""
        try:
            logger.info("[LOAD] Loading multi-timeframe training data...")

            # Define timeframes to process
            timeframes = ['1min', '3min', '5min', '15min']
            underlyings = ['NIFTY', 'BANKNIFTY']

            all_training_data = []

            # Load feature data from all timeframes separately
            timeframe_datasets = {}
            for timeframe in timeframes:
                timeframe_data = await self._load_timeframe_features(timeframe, underlyings)
                if timeframe_data is not None and timeframe_data.height > 0:
                    # Add timeframe identifier before any timezone operations
                    timeframe_data = timeframe_data.with_columns([
                        pl.lit(timeframe).alias('timeframe')
                    ])

                    # Handle timezone issues by converting to naive datetime
                    if 'timestamp' in timeframe_data.columns:
                        try:
                            timeframe_data = timeframe_data.with_columns([
                                pl.col('timestamp').dt.replace_time_zone(None).alias('timestamp')
                            ])
                        except Exception as e:
                            logger.warning(f"[WARNING] Timezone conversion failed for {timeframe}: {e}")

                    timeframe_datasets[timeframe] = timeframe_data
                    all_training_data.append(timeframe_data)
                    logger.info(f"[LOAD] Loaded {timeframe_data.height} records from {timeframe}")

            # Load backtest results for strategy targets
            backtest_data = await self._load_backtest_training_data()
            if backtest_data is not None and backtest_data.height > 0:
                all_training_data.append(backtest_data)
                logger.info(f"[LOAD] Loaded {backtest_data.height} backtest training records")

            if not all_training_data:
                logger.warning("[WARNING] No training data found")
                return None

            # Combine all training data
            if len(all_training_data) == 1:
                combined_data = all_training_data[0]
            else:
                # Concatenate all data with timezone handling
                try:
                    # Standardize timestamp columns before concatenation
                    standardized_data = []
                    for df in all_training_data:
                        if 'timestamp' in df.columns:
                            # Convert timestamp to consistent format
                            df = df.with_columns([
                                pl.col('timestamp').dt.replace_time_zone(None).alias('timestamp')
                            ])
                        standardized_data.append(df)

                    combined_data = pl.concat(standardized_data, how="diagonal")
                except Exception as e:
                    logger.warning(f"[WARNING] Failed to combine training data: {e}")
                    # If concat fails, use the largest dataset
                    combined_data = max(all_training_data, key=lambda x: x.height)

            logger.info(f"[SUCCESS] Loaded {combined_data.height} total training samples")
            return combined_data

        except Exception as e:
            logger.error(f"[ERROR] Failed to load training data: {e}")
            return None

    async def _create_target_variables(self, data: pl.DataFrame) -> pl.DataFrame:
        """Create target variables from available data"""
        try:
            logger.info("[TARGET] Creating target variables from available data...")

            # Create option_price target from close price
            if 'close' in data.columns and 'option_price' not in data.columns:
                data = data.with_columns([
                    pl.col('close').alias('option_price')
                ])

            # Create implied_vol target (simplified calculation)
            if 'implied_vol' not in data.columns:
                if 'close' in data.columns:
                    # Simple volatility proxy using price changes
                    data = data.with_columns([
                        pl.col('close').pct_change().rolling_std(window_size=20).alias('implied_vol')
                    ])
                else:
                    data = data.with_columns([
                        pl.lit(0.2).alias('implied_vol')  # Default 20% volatility
                    ])

            # Create strategy_return target from price changes
            if 'strategy_return' not in data.columns:
                if 'close' in data.columns:
                    data = data.with_columns([
                        pl.col('close').pct_change().alias('strategy_return')
                    ])
                else:
                    data = data.with_columns([
                        pl.lit(0.0).alias('strategy_return')
                    ])

            # Create annualized_return target
            if 'annualized_return' not in data.columns:
                data = data.with_columns([
                    (pl.col('strategy_return') * 252).alias('annualized_return')  # Annualize daily returns
                ])

            # Create sharpe_ratio target
            if 'sharpe_ratio' not in data.columns:
                data = data.with_columns([
                    (pl.col('strategy_return') / pl.col('implied_vol')).alias('sharpe_ratio')
                ])

            logger.info(f"[TARGET] Created target variables for {data.height} records")
            return data

        except Exception as e:
            logger.error(f"[ERROR] Failed to create target variables: {e}")
            return data

    async def _load_and_process_timeframe_data(self, timeframe: str, underlyings: list) -> Optional[pl.DataFrame]:
        """Load and process data for a specific timeframe"""
        try:
            logger.info(f"[LOAD_TF] Loading {timeframe} timeframe data separately...")

            # Load timeframe features
            timeframe_data = await self._load_timeframe_features(timeframe, underlyings)
            if timeframe_data is None or timeframe_data.height == 0:
                return None

            # Add timeframe identifier
            timeframe_data = timeframe_data.with_columns([
                pl.lit(timeframe).alias('timeframe')
            ])

            # Handle timezone issues
            if 'timestamp' in timeframe_data.columns:
                try:
                    timeframe_data = timeframe_data.with_columns([
                        pl.col('timestamp').dt.replace_time_zone(None).alias('timestamp')
                    ])
                except Exception as e:
                    logger.warning(f"[WARNING] Timezone conversion failed for {timeframe}: {e}")

            # Load and integrate index reference data for this timeframe
            if self.config['use_index_reference']:
                index_data = await self._load_timeframe_index_data(timeframe)
                if index_data is not None:
                    timeframe_data = await self._calculate_index_reference_features(timeframe_data, index_data)

            # Create target variables
            timeframe_data = await self._create_target_variables(timeframe_data)

            logger.info(f"[LOAD_TF] Processed {timeframe_data.height} records for {timeframe}")
            return timeframe_data

        except Exception as e:
            logger.error(f"[ERROR] Failed to load {timeframe} data: {e}")
            return None

    async def _load_timeframe_index_data(self, timeframe: str) -> Optional[pl.DataFrame]:
        """Load index data for a specific timeframe"""
        try:
            historical_path = self.data_path / "historical" / timeframe
            if not historical_path.exists():
                return None

            index_files = list(historical_path.glob("*.parquet"))
            if not index_files:
                return None

            all_index_data = []
            for file in index_files:
                try:
                    df = pl.read_parquet(file)

                    # Handle different column names
                    if 'underlying' not in df.columns and 'symbol' in df.columns:
                        df = df.rename({'symbol': 'underlying'})

                    # Filter for index data
                    if 'option_type' in df.columns:
                        index_df = df.filter(
                            pl.col('option_type').is_null() &
                            pl.col('underlying').is_in(['NIFTY', 'BANKNIFTY'])
                        )
                    else:
                        index_df = df.filter(
                            pl.col('underlying').is_in(['NIFTY', 'BANKNIFTY'])
                        ) if 'underlying' in df.columns else df

                    if index_df.height > 0:
                        # Handle timezone
                        if 'timestamp' in index_df.columns:
                            try:
                                index_df = index_df.with_columns([
                                    pl.col('timestamp').dt.replace_time_zone(None).alias('timestamp')
                                ])
                            except:
                                pass
                        all_index_data.append(index_df)
                except Exception as e:
                    logger.warning(f"[WARNING] Failed to load {file}: {e}")

            if all_index_data:
                combined_index = pl.concat(all_index_data, how="diagonal")
                if 'timestamp' in combined_index.columns and 'underlying' in combined_index.columns:
                    combined_index = combined_index.unique(['timestamp', 'underlying'])
                return combined_index

            return None

        except Exception as e:
            logger.error(f"[ERROR] Failed to load {timeframe} index data: {e}")
            return None

    async def _load_timeframe_features(self, timeframe: str, underlyings: list) -> Optional[pl.DataFrame]:
        """Load feature data for a specific timeframe"""
        try:
            timeframe_path = self.features_path / timeframe
            if not timeframe_path.exists():
                logger.warning(f"[WARNING] No {timeframe} features directory found")
                return None

            all_features = []

            for underlying in underlyings:
                # Load main features file
                feature_files = list(timeframe_path.glob(f"{underlying}_{timeframe}_features_*.parquet"))
                if feature_files:
                    latest_features = max(feature_files, key=lambda x: x.stat().st_mtime)
                    features_df = pl.read_parquet(latest_features)

                    # Filter for PE/CE options data if enabled
                    if self.config['pe_ce_filter'] and 'option_type' in features_df.columns:
                        pe_ce_features = features_df.filter(
                            pl.col('option_type').is_not_null() &
                            pl.col('option_type').is_in(['PE', 'CE'])
                        )
                        if pe_ce_features.height > 0:
                            all_features.append(pe_ce_features)
                            logger.info(f"[LOAD] {underlying} {timeframe}: {pe_ce_features.height} PE/CE records")
                    else:
                        all_features.append(features_df)
                        logger.info(f"[LOAD] {underlying} {timeframe}: {features_df.height} feature records")

                # Load additional feature types (options, technical, etc.)
                for feature_type in ['options', 'technical', 'quant', 'regime']:
                    type_files = list(timeframe_path.glob(f"{underlying}_{timeframe}_{feature_type}_*.parquet"))
                    if type_files:
                        latest_type = max(type_files, key=lambda x: x.stat().st_mtime)
                        type_df = pl.read_parquet(latest_type)

                        # Add feature type identifier
                        type_df = type_df.with_columns([
                            pl.lit(feature_type).alias('feature_type')
                        ])
                        all_features.append(type_df)
                        logger.info(f"[LOAD] {underlying} {timeframe} {feature_type}: {type_df.height} records")

            if all_features:
                # Combine all features for this timeframe
                combined_features = pl.concat(all_features, how="diagonal")
                return combined_features
            else:
                return None

        except Exception as e:
            logger.error(f"[ERROR] Failed to load {timeframe} features: {e}")
            return None

    async def _load_backtest_training_data(self) -> Optional[pl.DataFrame]:
        """Load backtest results for training"""
        try:
            # Look for strategy performance files in ai_training directory
            ai_training_path = self.data_path / "ai_training"
            if not ai_training_path.exists():
                logger.warning("[WARNING] No ai_training directory found")
                return None

            strategy_files = list(ai_training_path.glob("strategy_performance_*.parquet"))
            if not strategy_files:
                logger.warning("[WARNING] No strategy performance files found")
                return None

            # Load the latest strategy performance file
            latest_strategy = max(strategy_files, key=lambda x: x.stat().st_mtime)
            strategy_df = pl.read_parquet(latest_strategy)

            logger.info(f"[LOAD] Loaded {strategy_df.height} strategy performance records")
            return strategy_df

        except Exception as e:
            logger.error(f"[ERROR] Failed to load backtest training data: {e}")
            return None

    async def _integrate_index_reference_data(self, training_data: pl.DataFrame) -> pl.DataFrame:
        """Integrate index data as reference features for PE/CE training"""
        try:
            logger.info("[INDEX_REF] Integrating index reference data...")

            # Load index data from historical sources
            index_data = await self._load_index_reference_data()
            if index_data is None:
                logger.warning("[WARNING] No index reference data found")
                return training_data

            # Calculate index-based reference features
            enhanced_data = await self._calculate_index_reference_features(training_data, index_data)

            logger.info(f"[SUCCESS] Enhanced training data with index reference features")
            return enhanced_data

        except Exception as e:
            logger.error(f"[ERROR] Failed to integrate index reference data: {e}")
            return training_data

    async def _train_timeframe_target_models(self, data: pl.DataFrame, target: str, timeframe: str):
        """Train models for specific target and timeframe"""
        try:
            start_time = time.time()
            logger.info(f"[TRAIN] Training {timeframe} models for target: {target}")

            # Prepare data
            if target not in data.columns:
                logger.warning(f"[WARNING] Target {target} not found in {timeframe} data")
                return

            # Timeframe-specific feature selection
            feature_cols = self._get_timeframe_features(data.columns, timeframe)

            if not feature_cols:
                logger.warning(f"[WARNING] No features found for {timeframe} {target}")
                return

            # Convert to pandas first for better numpy compatibility
            X_df = data.select(feature_cols).to_pandas()
            y_series = data.select([target]).to_pandas()[target]

            # Convert to numpy arrays
            X = X_df.values
            y = y_series.values

            # Remove NaN values
            mask = ~(np.isnan(X).any(axis=1) | np.isnan(y))
            X, y = X[mask], y[mask]

            if len(X) == 0:
                logger.warning(f"[WARNING] No valid data for {timeframe} {target}")
                return

            # Train models for this timeframe
            for model_type in self.config['model_types']:
                model_key = f"{timeframe}_{target}_{model_type}"

                if model_type == 'lightgbm':
                    model = await self._train_lightgbm(X, y, model_key)
                elif model_type == 'tabnet':
                    model = await self._train_tabnet(X, y, model_key)

                if model:
                    self.trained_models[model_key] = {
                        'model': model,
                        'timeframe': timeframe,
                        'target': target,
                        'model_type': model_type,
                        'features': feature_cols
                    }
                    elapsed_time = time.time() - start_time
                    logger.info(f"[SUCCESS] Trained {model_key} in {elapsed_time:.1f}s")

        except Exception as e:
            logger.error(f"[ERROR] Failed to train {timeframe} {target} models: {e}")

    def _get_timeframe_features(self, columns: list, timeframe: str) -> list:
        """Get relevant features for specific timeframe"""
        try:
            # Base features that are always relevant
            base_features = [
                'delta', 'gamma', 'theta', 'vega', 'iv_rank',
                'moneyness', 'time_to_expiry'
            ]

            # Timeframe-specific features
            timeframe_features = {
                '1min': ['rsi_1', 'sma_5', 'ema_5', 'volume_ratio'],
                '3min': ['rsi_3', 'sma_15', 'ema_15', 'momentum_3'],
                '5min': ['rsi_5', 'sma_20', 'ema_20', 'momentum_5'],
                '15min': ['rsi_15', 'sma_50', 'ema_50', 'momentum_15']
            }

            # Index reference features
            index_features = [
                'index_price', 'index_return_1min', 'index_return_5min',
                'index_return_15min', 'index_volatility'
            ]

            # Combined features
            combined_features = [
                'delta_exposure', 'gamma_risk', 'vega_exposure', 'theta_decay_rate'
            ]

            # Collect all relevant features
            relevant_features = base_features + index_features + combined_features
            if timeframe in timeframe_features:
                relevant_features.extend(timeframe_features[timeframe])

            # Filter for features that actually exist in the data
            available_features = [col for col in relevant_features if col in columns]

            logger.info(f"[FEATURES] {timeframe}: {len(available_features)} features selected")
            return available_features

        except Exception as e:
            logger.error(f"[ERROR] Failed to get {timeframe} features: {e}")
            return []

    async def _train_ensemble_models(self, data: pl.DataFrame):
        """Train ensemble models combining all timeframes"""
        try:
            logger.info("[ENSEMBLE] Training ensemble models...")

            # Create timeframe-aggregated features
            ensemble_data = await self._create_ensemble_features(data)

            if ensemble_data is None or ensemble_data.height == 0:
                logger.warning("[WARNING] No ensemble data available")
                return

            # Train ensemble models for each target
            for target in self.config['target_variables']:
                if target not in ensemble_data.columns:
                    continue

                # Get all available features
                feature_cols = [col for col in self.config['feature_columns']
                              if col in ensemble_data.columns]

                if not feature_cols:
                    continue

                # Convert to pandas first for better numpy compatibility
                X_df = ensemble_data.select(feature_cols).to_pandas()
                y_series = ensemble_data.select([target]).to_pandas()[target]

                # Convert to numpy arrays
                X = X_df.values
                y = y_series.values

                # Remove NaN values
                mask = ~(np.isnan(X).any(axis=1) | np.isnan(y))
                X, y = X[mask], y[mask]

                if len(X) == 0:
                    continue

                # Train ensemble model
                model_key = f"ensemble_{target}_lightgbm"
                model = await self._train_lightgbm(X, y, model_key)

                if model:
                    self.trained_models[model_key] = {
                        'model': model,
                        'timeframe': 'ensemble',
                        'target': target,
                        'model_type': 'lightgbm',
                        'features': feature_cols
                    }
                    logger.info(f"[SUCCESS] Trained ensemble model for {target}")

        except Exception as e:
            logger.error(f"[ERROR] Failed to train ensemble models: {e}")

    async def _create_ensemble_features(self, data: pl.DataFrame) -> Optional[pl.DataFrame]:
        """Create features that combine information from all timeframes"""
        try:
            if 'timeframe' not in data.columns:
                return data

            # Check if we have the required columns for grouping
            group_cols = []
            if 'timestamp' in data.columns:
                group_cols.append('timestamp')
            if 'underlying' in data.columns:
                group_cols.append('underlying')
            elif 'symbol' in data.columns:
                group_cols.append('symbol')

            if not group_cols:
                logger.warning("[WARNING] No grouping columns found for ensemble features")
                return data

            # Get available numeric columns for aggregation
            available_cols = data.columns
            numeric_cols = []

            # Check for common feature columns
            for col in ['delta', 'gamma', 'theta', 'vega', 'iv_rank', 'close', 'volume']:
                if col in available_cols:
                    numeric_cols.append(col)

            if not numeric_cols:
                logger.warning("[WARNING] No numeric columns found for ensemble aggregation")
                return data

            # Create aggregation expressions
            agg_exprs = []
            for col in numeric_cols:
                agg_exprs.extend([
                    pl.col(col).mean().alias(f'{col}_avg'),
                    pl.col(col).std().alias(f'{col}_std'),
                    pl.col(col).first().alias(f'{col}_first'),
                    pl.col(col).last().alias(f'{col}_last')
                ])

            # Group by available columns and aggregate
            timeframe_groups = data.group_by(group_cols).agg(agg_exprs)

            logger.info(f"[ENSEMBLE] Created ensemble features: {timeframe_groups.height} records")
            return timeframe_groups

        except Exception as e:
            logger.error(f"[ERROR] Failed to create ensemble features: {e}")
            return None

    async def _load_index_reference_data(self) -> Optional[pl.DataFrame]:
        """Load index data for reference features"""
        try:
            logger.info("[INDEX_REF] Loading index reference data...")

            # Load index data from multiple sources
            all_index_data = []

            # 1. Load from historical data (multi-timeframe)
            historical_path = self.data_path / "historical"
            timeframes = ['1min', '3min', '5min', '15min']

            for timeframe in timeframes:
                timeframe_path = historical_path / timeframe
                if timeframe_path.exists():
                    index_files = list(timeframe_path.glob("*.parquet"))
                    for file in index_files:
                        try:
                            df = pl.read_parquet(file)

                            # Handle different column names (symbol vs underlying)
                            if 'underlying' not in df.columns and 'symbol' in df.columns:
                                df = df.rename({'symbol': 'underlying'})

                            # Filter for index data (underlying symbols without option_type)
                            if 'option_type' in df.columns:
                                index_df = df.filter(
                                    pl.col('option_type').is_null() &
                                    pl.col('underlying').is_in(['NIFTY', 'BANKNIFTY'])
                                )
                            else:
                                # If no option_type column, assume it's index data
                                index_df = df.filter(
                                    pl.col('underlying').is_in(['NIFTY', 'BANKNIFTY'])
                                ) if 'underlying' in df.columns else df

                            if index_df.height > 0:
                                # Add timeframe identifier
                                index_df = index_df.with_columns([
                                    pl.lit(timeframe).alias('source_timeframe')
                                ])
                                all_index_data.append(index_df)
                                logger.info(f"[INDEX_REF] Loaded {index_df.height} {timeframe} index records")
                        except Exception as e:
                            logger.warning(f"[WARNING] Failed to load {file}: {e}")

            # 2. Load from feature engineering outputs (underlying data)
            features_path = self.data_path / "features"
            for timeframe in timeframes:
                timeframe_path = features_path / timeframe
                if timeframe_path.exists():
                    # Look for underlying OHLC data in feature files
                    for underlying in ['NIFTY', 'BANKNIFTY']:
                        feature_files = list(timeframe_path.glob(f"{underlying}_{timeframe}_features_*.parquet"))
                        if feature_files:
                            latest_file = max(feature_files, key=lambda x: x.stat().st_mtime)
                            try:
                                df = pl.read_parquet(latest_file)

                                # Extract underlying price data (not options)
                                if 'option_type' in df.columns:
                                    underlying_df = df.filter(pl.col('option_type').is_null())
                                else:
                                    # Look for OHLC columns to identify underlying data
                                    ohlc_cols = ['open', 'high', 'low', 'close']
                                    if all(col in df.columns for col in ohlc_cols):
                                        underlying_df = df
                                    else:
                                        continue

                                if underlying_df.height > 0:
                                    underlying_df = underlying_df.with_columns([
                                        pl.lit(timeframe).alias('source_timeframe'),
                                        pl.lit(underlying).alias('underlying')
                                    ])
                                    all_index_data.append(underlying_df)
                                    logger.info(f"[INDEX_REF] Loaded {underlying_df.height} {underlying} {timeframe} underlying records")
                            except Exception as e:
                                logger.warning(f"[WARNING] Failed to load {latest_file}: {e}")

            if not all_index_data:
                logger.warning("[WARNING] No index reference data found")
                return None

            # Combine all index data with timezone handling
            try:
                # Standardize timestamp columns before concatenation
                standardized_index_data = []
                for df in all_index_data:
                    if 'timestamp' in df.columns:
                        # Convert timestamp to consistent format
                        df = df.with_columns([
                            pl.col('timestamp').dt.replace_time_zone(None).alias('timestamp')
                        ])
                    standardized_index_data.append(df)

                combined_index = pl.concat(standardized_index_data, how="diagonal")

                # Remove duplicates based on timestamp and underlying
                if 'timestamp' in combined_index.columns and 'underlying' in combined_index.columns:
                    combined_index = combined_index.unique(['timestamp', 'underlying'])

                logger.info(f"[SUCCESS] Loaded {combined_index.height} index reference records")
                return combined_index
            except Exception as e:
                logger.error(f"[ERROR] Failed to combine index data: {e}")
                # Return the largest dataset if combination fails
                if all_index_data:
                    return max(all_index_data, key=lambda x: x.height)
                return None

        except Exception as e:
            logger.error(f"[ERROR] Failed to load index reference data: {e}")
            return None

    async def _calculate_index_reference_features(self, training_data: pl.DataFrame, index_data: pl.DataFrame) -> pl.DataFrame:
        """Calculate comprehensive index-based reference features for PE/CE analysis"""
        try:
            logger.info("[INDEX_REF] Calculating comprehensive index reference features...")

            # Ensure we have the required columns
            if 'close' not in index_data.columns:
                logger.warning("[WARNING] No 'close' column in index data")
                return training_data

            # Calculate comprehensive index features
            index_features = index_data.with_columns([
                # Index price
                pl.col('close').alias('index_price'),
                # Multi-timeframe returns
                pl.col('close').pct_change().alias('index_return_1min'),
                pl.col('close').pct_change(3).alias('index_return_3min'),
                pl.col('close').pct_change(5).alias('index_return_5min'),
                pl.col('close').pct_change(15).alias('index_return_15min'),
                # Volatility measures
                pl.col('close').pct_change().rolling_std(window_size=10).alias('index_volatility_10'),
                pl.col('close').pct_change().rolling_std(window_size=20).alias('index_volatility'),
                pl.col('close').pct_change().rolling_std(window_size=50).alias('index_volatility_50'),
                # Momentum indicators
                (pl.col('close') / pl.col('close').shift(10) - 1).alias('index_momentum_10'),
                (pl.col('close') / pl.col('close').shift(20) - 1).alias('index_momentum_20'),
                # Trend indicators
                (pl.col('close') > pl.col('close').rolling_mean(20)).alias('index_above_sma20'),
                (pl.col('close') > pl.col('close').rolling_mean(50)).alias('index_above_sma50')
            ])

            # Join with training data
            join_cols = ['timestamp', 'underlying'] if 'underlying' in training_data.columns else ['timestamp']

            enhanced_data = training_data.join(
                index_features.select(['timestamp', 'underlying', 'index_price', 'index_return_1min',
                                     'index_return_3min', 'index_return_5min', 'index_return_15min',
                                     'index_volatility', 'index_volatility_10', 'index_volatility_50',
                                     'index_momentum_10', 'index_momentum_20', 'index_above_sma20', 'index_above_sma50']),
                on=join_cols,
                how='left'
            )

            # Calculate PE/CE specific features
            if 'strike_price' in enhanced_data.columns and 'index_price' in enhanced_data.columns:
                enhanced_data = enhanced_data.with_columns([
                    # Moneyness calculations
                    (pl.col('index_price') / pl.col('strike_price')).alias('moneyness'),
                    ((pl.col('index_price') / pl.col('strike_price')) - 1).alias('moneyness_deviation'),

                    # Distance from ATM for PE/CE
                    pl.when(pl.col('option_type') == 'CE')
                    .then((pl.col('index_price') - pl.col('strike_price')) / pl.col('strike_price'))
                    .when(pl.col('option_type') == 'PE')
                    .then((pl.col('strike_price') - pl.col('index_price')) / pl.col('strike_price'))
                    .otherwise(0)
                    .alias('distance_from_atm'),

                    # ITM/OTM classification
                    pl.when(pl.col('option_type') == 'CE')
                    .then(pl.col('index_price') > pl.col('strike_price'))
                    .when(pl.col('option_type') == 'PE')
                    .then(pl.col('index_price') < pl.col('strike_price'))
                    .otherwise(False)
                    .alias('is_itm')
                ])

            # Calculate Greeks-based exposures if Greeks are available
            if all(col in enhanced_data.columns for col in ['delta', 'gamma', 'theta', 'vega']):
                enhanced_data = enhanced_data.with_columns([
                    # Delta exposure
                    (pl.col('delta').fill_null(0) * pl.col('index_price').fill_null(0)).alias('delta_exposure'),
                    (pl.col('delta').fill_null(0) * pl.col('index_return_1min').fill_null(0)).alias('delta_pnl_1min'),
                    # Gamma risk
                    (pl.col('gamma').fill_null(0) * pl.col('index_price').fill_null(0)).alias('gamma_risk'),
                    # Vega exposure
                    (pl.col('vega').fill_null(0) * pl.col('index_volatility').fill_null(0.2)).alias('vega_exposure'),
                    # Theta decay rate
                    (pl.col('theta').fill_null(0) / pl.col('index_price').fill_null(1)).alias('theta_decay_rate')
                ])

            # Add PE/CE specific analysis features
            if 'option_type' in enhanced_data.columns:
                enhanced_data = enhanced_data.with_columns([
                    # PE/CE indicators
                    pl.when(pl.col('option_type') == 'PE').then(1).otherwise(0).alias('is_pe'),
                    pl.when(pl.col('option_type') == 'CE').then(1).otherwise(0).alias('is_ce'),

                    # Direction alignment with index
                    pl.when((pl.col('option_type') == 'CE') & (pl.col('index_return_1min') > 0))
                    .then(1)
                    .when((pl.col('option_type') == 'PE') & (pl.col('index_return_1min') < 0))
                    .then(1)
                    .otherwise(0)
                    .alias('direction_alignment')
                ])

            # Add time to expiry (simplified)
            enhanced_data = enhanced_data.with_columns([
                pl.lit(30.0).alias('time_to_expiry')  # Default 30 days
            ])

            logger.info(f"[SUCCESS] Enhanced {enhanced_data.height} records with comprehensive index features")
            return enhanced_data

        except Exception as e:
            logger.error(f"[ERROR] Failed to calculate index reference features: {e}")
            return training_data

    async def _train_target_models(self, data: pl.DataFrame, target: str):
        """Train models for specific target"""
        try:
            logger.info(f"[TRAIN] Training models for target: {target}")
            
            # Prepare data
            if target not in data.columns:
                logger.warning(f"[WARNING] Target {target} not found in data")
                return
            
            # Feature selection
            feature_cols = [col for col in self.config['feature_columns'] if col in data.columns]
            
            X = data.select(feature_cols).to_numpy()
            y = data[target].to_numpy()
            
            # Remove NaN values using pyarrow compute
            mask = ~(pc.is_null(X).any() | pc.is_null(y))
            X, y = X.filter(mask), y.filter(mask)
            
            if len(X) == 0:
                logger.warning(f"[WARNING] No valid data for target {target}")
                return
            
            # Train LightGBM
            lgb_model = await self._train_lightgbm(X, y, target)
            if lgb_model:
                self.trained_models[f"{target}_lightgbm"] = lgb_model
            
            # Train TabNet if available
            try:
                tabnet_model = await self._train_tabnet(X, y, target)
                if tabnet_model:
                    self.trained_models[f"{target}_tabnet"] = tabnet_model
            except Exception as e:
                logger.warning(f"[WARNING] TabNet training failed: {e}")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to train models for {target}: {e}")
    
    async def _train_lightgbm(self, X: np.ndarray, y: np.ndarray, target: str):
        """Train LightGBM model with Optuna optimization"""
        try:
            def objective(trial):
                params = {
                    'objective': 'regression',
                    'metric': 'rmse',
                    'boosting_type': 'gbdt',
                    'num_leaves': trial.suggest_int('num_leaves', 10, 300),
                    'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3),
                    'feature_fraction': trial.suggest_float('feature_fraction', 0.4, 1.0),
                    'bagging_fraction': trial.suggest_float('bagging_fraction', 0.4, 1.0),
                    'bagging_freq': trial.suggest_int('bagging_freq', 1, 7),
                    'min_child_samples': trial.suggest_int('min_child_samples', 5, 100),
                    'verbosity': -1
                }
                
                # Time series cross-validation
                tscv = TimeSeriesSplit(n_splits=self.config['cv_folds'])
                scores = []
                
                for train_idx, val_idx in tscv.split(X):
                    X_train, X_val = X[train_idx], X[val_idx]
                    y_train, y_val = y[train_idx], y[val_idx]
                    
                    train_data = lgb.Dataset(X_train, label=y_train)
                    val_data = lgb.Dataset(X_val, label=y_val, reference=train_data)
                    
                    model = lgb.train(
                        params,
                        train_data,
                        valid_sets=[val_data],
                        num_boost_round=500,  # Reduced from 1000 for faster training
                        callbacks=[lgb.early_stopping(20), lgb.log_evaluation(0)]  # Reduced early stopping patience
                    )
                    
                    y_pred = model.predict(X_val)
                    score = mean_squared_error(y_val, y_pred)
                    scores.append(score)
                
                return pa.compute.mean(pa.array(scores)).as_py()
            
            # Optimize hyperparameters with timeout and pruning
            study = optuna.create_study(
                direction='minimize',
                pruner=optuna.pruners.MedianPruner(n_startup_trials=3, n_warmup_steps=5)
            )

            # Add timeout to prevent excessive optimization time
            timeout = self.config.get('optuna_timeout', 60)  # 60 seconds default
            study.optimize(
                objective,
                n_trials=self.config['optuna_trials'],
                timeout=timeout,
                show_progress_bar=False  # Reduce logging overhead
            )
            
            # Train final model with best parameters
            best_params = study.best_params
            best_params.update({
                'objective': 'regression',
                'metric': 'rmse',
                'verbosity': -1,
                'num_threads': 2  # Limit threads for parallel training
            })

            train_data = lgb.Dataset(X, label=y)
            final_model = lgb.train(
                best_params,
                train_data,
                num_boost_round=300  # Reduced from 1000 for faster final training
            )
            
            logger.info(f"[SUCCESS] LightGBM trained for {target}")
            return final_model
            
        except Exception as e:
            logger.error(f"[ERROR] LightGBM training failed for {target}: {e}")
            return None
    
    async def _train_tabnet(self, X: np.ndarray, y: np.ndarray, target: str):
        """Train TabNet model"""
        try:
            # Split data
            split_idx = int(0.8 * len(X))
            X_train, X_val = X[:split_idx], X[split_idx:]
            y_train, y_val = y[:split_idx], y[split_idx:]
            
            # Initialize TabNet - optimized for faster training
            model = TabNetRegressor(
                n_d=16,    # Reduced from 32 to 16 for faster training
                n_a=16,    # Reduced from 32 to 16 for faster training
                n_steps=3, # Reduced from 5 to 3 for faster training
                gamma=1.3,
                lambda_sparse=1e-3,
                optimizer_fn=torch.optim.Adam,
                optimizer_params=dict(lr=2e-2),
                mask_type='entmax',
                scheduler_params={"step_size": 30, "gamma": 0.9},  # Reduced step size
                scheduler_fn=torch.optim.lr_scheduler.StepLR,
                verbose=0
            )
            
            # Train model - optimized for faster training
            model.fit(
                X_train, y_train.reshape(-1, 1),
                eval_set=[(X_val, y_val.reshape(-1, 1))],
                max_epochs=100,  # Reduced from 200 to 100 for faster training
                patience=10,     # Reduced from 20 to 10 for faster early stopping
                batch_size=1024,
                virtual_batch_size=128,
                drop_last=False
            )
            
            logger.info(f"[SUCCESS] TabNet trained for {target}")
            return model
            
        except Exception as e:
            logger.error(f"[ERROR] TabNet training failed for {target}: {e}")
            return None
    
    async def _save_models(self):
        """Save trained models with comprehensive metadata"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

            saved_models = {}

            for model_name, model_info in self.trained_models.items():
                try:
                    # Extract model and metadata
                    model = model_info['model']
                    timeframe = model_info.get('timeframe', 'unknown')
                    target = model_info.get('target', 'unknown')
                    model_type = model_info.get('model_type', 'unknown')
                    features = model_info.get('features', [])

                    # Save model file
                    model_file = self.models_path / f"{model_name}_{timestamp}.joblib"
                    joblib.dump(model, model_file)

                    # Store model metadata
                    saved_models[model_name] = {
                        'file_path': str(model_file),
                        'timeframe': timeframe,
                        'target': target,
                        'model_type': model_type,
                        'features': features,
                        'feature_count': len(features)
                    }

                    logger.info(f"[SAVE] Saved {timeframe} {target} {model_type} model: {model_name}")

                except Exception as e:
                    logger.error(f"[ERROR] Failed to save model {model_name}: {e}")

            # Save comprehensive model metadata
            metadata = {
                'timestamp': timestamp,
                'total_models': len(saved_models),
                'models': saved_models,
                'config': self.config,
                'training_summary': {
                    'timeframes_trained': list(set([info['timeframe'] for info in saved_models.values()])),
                    'targets_trained': list(set([info['target'] for info in saved_models.values()])),
                    'model_types_used': list(set([info['model_type'] for info in saved_models.values()])),
                    'pe_ce_focused': self.config.get('pe_ce_filter', False),
                    'index_reference_used': self.config.get('use_index_reference', False)
                }
            }

            metadata_file = self.models_path / f"model_metadata_{timestamp}.json"
            with open(metadata_file, 'w') as f:
                json.dump(metadata, f, indent=2, default=str)

            logger.info(f"[SUCCESS] Saved {len(saved_models)} models with metadata")

        except Exception as e:
            logger.error(f"[ERROR] Failed to save models: {e}")
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            logger.info("[CLEANUP] Cleaning up Options AI Training Agent...")
            self.is_running = False
            logger.info("[SUCCESS] Options AI Training Agent cleaned up")
        except Exception as e:
            logger.error(f"[ERROR] Cleanup failed: {e}")

# Example usage
async def main():
    agent = OptionsAITrainingAgent()
    try:
        await agent.initialize()
        await agent.start()
    except KeyboardInterrupt:
        logger.info("Agent interrupted by user")
    finally:
        await agent.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
