# 🎯 Options Trading Strategies Configuration
# Real-time signal generation strategies for NIFTY & BANKNIFTY

strategies:
  # Volatility-based strategies
  volatility_breakout_ce:
    name: "Volatility Breakout Call"
    type: "volatility"
    market_outlook: "bullish"
    timeframes: ["1min", "3min", "5min"]
    parameters:
      entry_conditions:
        - "rsi_14 > 60"
        - "underlying_above_ema_20"
        - "volume > avg_volume_20"
      confidence_threshold: 0.6
      max_signals_per_day: 5
    
  volatility_breakout_pe:
    name: "Volatility Breakout Put"
    type: "volatility"
    market_outlook: "bearish"
    timeframes: ["1min", "3min", "5min"]
    parameters:
      entry_conditions:
        - "rsi_14 < 40"
        - "underlying_below_ema_20"
        - "volume > avg_volume_20"
      confidence_threshold: 0.6
      max_signals_per_day: 5

  # Directional strategies
  momentum_long_call:
    name: "Momentum Long Call"
    type: "directional"
    market_outlook: "bullish"
    timeframes: ["3min", "5min", "15min"]
    parameters:
      entry_conditions:
        - "rsi_14 > 55"
        - "underlying_above_ema_20"
      confidence_threshold: 0.7
      max_signals_per_day: 3

  momentum_long_put:
    name: "Momentum Long Put"
    type: "directional"
    market_outlook: "bearish"
    timeframes: ["3min", "5min", "15min"]
    parameters:
      entry_conditions:
        - "rsi_14 < 45"
        - "underlying_below_ema_20"
      confidence_threshold: 0.7
      max_signals_per_day: 3

  # Mean reversion strategies
  oversold_bounce:
    name: "Oversold Bounce"
    type: "mean_reversion"
    market_outlook: "bullish"
    timeframes: ["5min", "15min"]
    parameters:
      entry_conditions:
        - "rsi_14 < 30"
        - "iv_rank > 50"
      confidence_threshold: 0.8
      max_signals_per_day: 2

  overbought_fade:
    name: "Overbought Fade"
    type: "mean_reversion"
    market_outlook: "bearish"
    timeframes: ["5min", "15min"]
    parameters:
      entry_conditions:
        - "rsi_14 > 70"
        - "iv_rank > 50"
      confidence_threshold: 0.8
      max_signals_per_day: 2

  # Flow-based strategies
  unusual_volume_ce:
    name: "Unusual Volume Call"
    type: "flow"
    market_outlook: "bullish"
    timeframes: ["1min", "3min"]
    parameters:
      entry_conditions:
        - "volume > avg_volume_20"
        - "underlying_above_ema_20"
      confidence_threshold: 0.6
      max_signals_per_day: 8

  unusual_volume_pe:
    name: "Unusual Volume Put"
    type: "flow"
    market_outlook: "bearish"
    timeframes: ["1min", "3min"]
    parameters:
      entry_conditions:
        - "volume > avg_volume_20"
        - "underlying_below_ema_20"
      confidence_threshold: 0.6
      max_signals_per_day: 8

  # Simple trend following
  trend_following_ce:
    name: "Trend Following Call"
    type: "trend"
    market_outlook: "bullish"
    timeframes: ["5min", "15min"]
    parameters:
      entry_conditions:
        - "underlying_above_ema_20"
      confidence_threshold: 0.5
      max_signals_per_day: 10

  trend_following_pe:
    name: "Trend Following Put"
    type: "trend"
    market_outlook: "bearish"
    timeframes: ["5min", "15min"]
    parameters:
      entry_conditions:
        - "underlying_below_ema_20"
      confidence_threshold: 0.5
      max_signals_per_day: 10

  # Simple always-signal strategies for testing
  simple_call_signal:
    name: "Simple Call Signal"
    type: "simple"
    market_outlook: "bullish"
    timeframes: ["1min", "3min", "5min"]
    parameters:
      entry_conditions: []  # No conditions - always signal
      confidence_threshold: 0.3
      max_signals_per_day: 50

  simple_put_signal:
    name: "Simple Put Signal"
    type: "simple"
    market_outlook: "bearish"
    timeframes: ["1min", "3min", "5min"]
    parameters:
      entry_conditions: []  # No conditions - always signal
      confidence_threshold: 0.3
      max_signals_per_day: 50

# Strategy selection settings
strategy_selection:
  max_active_strategies: 8
  rotation_enabled: true
  rotation_interval_minutes: 30
  
  # Strategy weights for selection
  strategy_weights:
    volatility: 0.3
    directional: 0.3
    mean_reversion: 0.2
    flow: 0.15
    trend: 0.05

# Risk management per strategy
risk_management:
  default_lot_size: 1
  max_lot_size: 3
  max_daily_loss_per_strategy: 5000
  max_concurrent_signals: 5
  
# Performance tracking
performance:
  track_strategy_performance: true
  min_trades_for_evaluation: 5
  performance_window_days: 7
