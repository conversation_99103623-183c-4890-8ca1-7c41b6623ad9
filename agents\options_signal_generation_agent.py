#!/usr/bin/env python3
"""
Options Signal Generation Agent - Real-time Options Trading Signals

Features:
📊 1. Multi-Strategy Signal Generation
- Volatility-based signals
- Directional signals from underlying
- Options flow signals
- Greeks-based signals

📈 2. ML-Powered Predictions
- Options price prediction
- Volatility forecasting
- Strategy performance prediction
- Risk assessment

⚡ 3. Real-time Processing
- Live market data integration
- Fast signal computation
- Multi-timeframe analysis
- Signal confidence scoring

🎯 4. Signal Validation
- Historical performance validation
- Risk-adjusted signal scoring
- Signal correlation analysis
- False signal filtering
"""

import asyncio
import logging
import os
import polars as pl
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
import json
import yaml
import polars_talib as pt
import joblib # Import joblib for loading models

logger = logging.getLogger(__name__)

class OptionsSignalGenerationAgent:
    """Options Signal Generation Agent for real-time trading signals"""
    
    def __init__(self, config_path: str = "config/options_signal_generation_config.yaml",
                 strategies_config_path: str = "config/options_strategies.yaml",
                 models_path: str = "data/models"): # Add models_path
        self.config_path = config_path
        self.strategies_config_path = strategies_config_path
        self.models_path = models_path # Store models path
        self.config = None
        self.strategies = {}
        self.ai_models = {} # To store loaded AI models
        self.is_running = False
        
        logger.info("[INIT] Options Signal Generation Agent initialized")
    
    async def initialize(self):
        """Initialize the agent"""
        try:
            await self._load_config()
            await self._load_strategies()
            await self._load_ai_models() # Load AI models
            logger.info("[SUCCESS] Options Signal Generation Agent initialized successfully")
            return True
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize agent: {e}")
            return False
    
    async def _load_config(self):
        """Load configuration"""
        # Default configuration
        default_config = {
            'signal_types': ['volatility', 'directional', 'flow', 'greeks'],
            'timeframes': ['1min', '3min', '5min', '15min'],
            'confidence_threshold': 0.7,
            'signal_interval': 30,  # seconds - reduced for more frequent signals
            'underlying_symbols': ['NIFTY', 'BANKNIFTY'],
            'data_path': 'data',
            'greeks_calculation': True,
            'chunk_size': 50000,
            'max_concurrent_tasks': 4,
            'use_lazy_loading': True,
            'batch_processing': True,
            'run_once': False,  # Enable continuous mode for live trading
            'continuous_mode': True,  # Enable continuous mode for live trading
            'model_inference_enabled': True,
            'live_data_path': 'data/live',
            'historical_data_path': 'data/historical',
            'indicators_data_path': 'data/indicators',
            'validate_data': True,
            'min_data_points': 50,
            'max_data_age_minutes': 5
        }

        # Try to load from file if it exists
        if os.path.exists(self.config_path):
            try:
                with open(self.config_path, 'r') as f:
                    file_config = yaml.safe_load(f)

                # Merge configurations properly
                self.config = default_config.copy()
                if file_config:
                    # Handle nested configuration structure
                    if 'signal_generation' in file_config:
                        self.config.update(file_config['signal_generation'])
                    if 'ai_models' in file_config:
                        self.config.update(file_config['ai_models'])
                    if 'data_sources' in file_config:
                        self.config.update(file_config['data_sources'])

                    # Also merge any top-level configs
                    for key, value in file_config.items():
                        if key not in ['signal_generation', 'ai_models', 'data_sources']:
                            self.config[key] = value

                logger.info(f"[CONFIG] Loaded configuration from {self.config_path}")
            except Exception as e:
                logger.warning(f"[CONFIG] Failed to load config file {self.config_path}: {e}. Using defaults.")
                self.config = default_config
        else:
            logger.warning(f"[CONFIG] Config file {self.config_path} not found. Using default configuration.")
            self.config = default_config

        # Ensure critical settings for live trading
        if self.config.get('continuous_mode', True):
            self.config['run_once'] = False
            logger.info("[CONFIG] Continuous mode enabled - signals will be generated continuously")

        logger.info(f"[CONFIG] Signal generation configuration loaded: {len(self.config)} parameters")
        logger.info(f"[CONFIG] Key settings - Continuous: {self.config['continuous_mode']}, Interval: {self.config['signal_interval']}s")

    async def _load_strategies(self):
        """Load strategy definitions from the YAML file."""
        try:
            with open(self.strategies_config_path, 'r', encoding='utf-8') as f:
                self.strategies = yaml.safe_load(f)
            logger.info(f"[CONFIG] Loaded {len(self.strategies)} strategy categories from {self.strategies_config_path}")

            # Debug: Log strategy names
            if self.strategies:
                for category, strategies in self.strategies.items():
                    if isinstance(strategies, dict):
                        strategy_names = list(strategies.keys())
                        logger.info(f"[CONFIG] Category '{category}': {len(strategy_names)} strategies - {strategy_names}")
                    else:
                        logger.info(f"[CONFIG] Category '{category}': {type(strategies)}")
            else:
                logger.warning("[CONFIG] No strategies loaded from config file")
        except FileNotFoundError:
            logger.error(f"[ERROR] Strategies config file not found at {self.strategies_config_path}")
            self.strategies = {}
        except yaml.YAMLError as e:
            logger.error(f"[ERROR] Error parsing strategies config YAML: {e}")
            self.strategies = {}
        except UnicodeDecodeError as e:
            logger.error(f"[ERROR] Unicode decode error in strategies config: {e}")
            self.strategies = {}

    async def _load_ai_models(self):
        """Load trained AI models from the models directory."""
        try:
            model_path = Path(self.models_path)
            if not model_path.exists():
                logger.warning(f"[WARNING] AI models directory not found at {self.models_path}")
                return

            # Load all .joblib models
            for model_file in model_path.glob("*.joblib"):
                try:
                    model_name = model_file.stem
                    self.ai_models[model_name] = joblib.load(model_file)
                    logger.info(f"[MODEL] Loaded AI model: {model_name}")
                except Exception as e:
                    logger.error(f"[ERROR] Failed to load model {model_file.name}: {e}")
            
            logger.info(f"[MODEL] Loaded {len(self.ai_models)} AI models.")

        except Exception as e:
            logger.error(f"[ERROR] Failed to load AI models: {e}")
    
    async def start(self, **kwargs) -> bool:
        """Start the signal generation agent"""
        try:
            logger.info("[START] Starting Options Signal Generation Agent... 🚀")
            self.is_running = True
            
            # Start signal generation
            await self._generate_signals()
            
            return True
        except Exception as e:
            logger.error(f"[ERROR] Failed to start agent: {e}")
            return False
    
    async def _generate_signals(self):
        """Generate trading signals with optimized async processing"""
        if self.config['run_once']:
            # Run once and exit (batch mode)
            await self._run_signal_generation_cycle()
            logger.info("[BATCH] Signal generation completed - exiting 👋")
            self.is_running = False
        else:
            # Continuous mode (for live trading)
            while self.is_running:
                await self._run_signal_generation_cycle()
                if self.is_running:  # Check if still running after cycle
                    logger.info(f"[CONTINUOUS] Waiting {self.config['signal_interval']} seconds before next cycle... ⏳")
                    await asyncio.sleep(self.config['signal_interval'])

    async def _run_signal_generation_cycle(self):
        """Run a single cycle of signal generation"""
        try:
            logger.info("[SIGNAL] Generating trading signals... ✨")

            # Create semaphore for concurrent task control
            semaphore = asyncio.Semaphore(self.config['max_concurrent_tasks'])

            # Create tasks for all timeframes concurrently
            tasks = []
            for timeframe in self.config['timeframes']:
                task = self._process_timeframe_async(timeframe, semaphore)
                tasks.append(task)

            # Execute all timeframe processing concurrently
            all_timeframe_signals = await asyncio.gather(*tasks, return_exceptions=True)

            # Filter out exceptions and flatten the list of signals
            valid_signals_per_timeframe = [s for s in all_timeframe_signals if isinstance(s, pl.DataFrame)]
            
            # Combine signals from all timeframes for multi-timeframe evaluation
            if valid_signals_per_timeframe:
                combined_signals_df = pl.concat(valid_signals_per_timeframe)
                logger.info(f"[SIGNAL] Total signals generated across all timeframes: {combined_signals_df.height} 📈")

                # Apply Multi-Timeframe Signal Evaluation (Feature 5)
                final_signals = await self._multi_timeframe_confirmation(combined_signals_df)
                logger.info(f"[SIGNAL] Final signals after multi-timeframe confirmation: {final_signals.height} ✅")

                # Apply any global agent overrides (Feature 12)
                final_signals = await self._accept_agent_overrides(final_signals)
                logger.info(f"[SIGNAL] Final signals after agent overrides: {final_signals.height} 🤝")

                # Save the final consolidated signals
                if final_signals.height > 0:
                    await self._save_signals('final_consolidated', 'multi_timeframe', final_signals)
                else:
                    logger.info("[SIGNAL] No final consolidated signals after all filtering. 🗑️")

            exceptions = [r for r in all_timeframe_signals if isinstance(r, Exception)]
            if exceptions:
                logger.warning(f"[WARNING] Some timeframe processing tasks failed: {len(exceptions)} exceptions ⚠️")
                for exc in exceptions:
                    logger.error(f"[ERROR] Task exception: {exc}")
            else:
                logger.info("[SUCCESS] All signal generation tasks completed successfully ✅")

        except Exception as e:
            logger.error(f"[ERROR] Signal generation cycle failed: {e}")
            self.is_running = False

    async def _process_timeframe_async(self, timeframe: str, semaphore: asyncio.Semaphore) -> Optional[pl.DataFrame]:
        """Process a single timeframe with async optimization and return generated signals."""
        async with semaphore:
            try:
                logger.info(f"[SIGNAL] Processing {timeframe} timeframe... ⏱️")

                # Load data for this timeframe using lazy loading
                timeframe_data_lazy = await self._load_timeframe_data_lazy(timeframe)
                if timeframe_data_lazy is None:
                    return pl.DataFrame() # Return empty DataFrame if no data

                timeframe_data = timeframe_data_lazy.collect() # Collect the data once for the timeframe
                if timeframe_data.height == 0:
                    logger.warning(f"[WARNING] No data collected for {timeframe} timeframe. Skipping strategy evaluation. 🚫")
                    return pl.DataFrame() # Return empty DataFrame

                # Extract underlying data for technical analysis
                # The data contains options data, but we need underlying price data for indicators
                underlying_data = await self._extract_underlying_data(timeframe_data, timeframe)

                if underlying_data.height == 0:
                    logger.warning(f"[WARNING] No underlying data available for {timeframe} timeframe. 🚫")
                    return pl.DataFrame()

                # Get market regime info for filtering
                market_regime = await self._get_market_regime_info()

                all_timeframe_signals = []
                # Create tasks for all strategies concurrently
                strategy_tasks = []
                for category, strategies_dict in self.strategies.items():
                    # Only process categories that contain actual strategies
                    if category in ['strategies', 'directional_strategies', 'volatility_strategies', 'spread_strategies', 'complex_strategies']:
                        for strategy_id, strategy_def in strategies_dict.items():
                            # Pass the underlying data for technical analysis
                            task = self._process_strategy_async(strategy_id, strategy_def, timeframe, underlying_data, market_regime, semaphore)
                            strategy_tasks.append(task)

                # Execute all strategy processing concurrently and collect results
                logger.info(f"[STRATEGY] Starting {len(strategy_tasks)} strategy tasks for {timeframe} timeframe...")
                try:
                    strategy_results = await asyncio.wait_for(
                        asyncio.gather(*strategy_tasks, return_exceptions=True),
                        timeout=10.0  # 10 second timeout for faster debugging
                    )
                    logger.info(f"[STRATEGY] Completed {len(strategy_results)} strategy tasks for {timeframe} timeframe")
                except asyncio.TimeoutError:
                    logger.error(f"[ERROR] Strategy processing timed out for {timeframe} timeframe after 10 seconds")
                    return pl.DataFrame()

                # Filter out exceptions and combine signals from this timeframe
                for result_df in strategy_results:
                    if isinstance(result_df, pl.DataFrame) and result_df.height > 0:
                        all_timeframe_signals.append(result_df)
                    elif isinstance(result_df, Exception):
                        logger.error(f"[ERROR] Strategy task failed in {timeframe} timeframe: {result_df}")

                if all_timeframe_signals:
                    return pl.concat(all_timeframe_signals)
                else:
                    return pl.DataFrame()

            except Exception as e:
                logger.error(f"[ERROR] Failed to process {timeframe} timeframe: {e}")
                return pl.DataFrame() # Ensure a DataFrame is always returned
    
    async def _process_strategy_async(self, strategy_id: str, strategy_def: Dict[str, Any], timeframe: str, data: pl.DataFrame, market_regime: Dict[str, Any], semaphore: asyncio.Semaphore) -> Optional[pl.DataFrame]:
        """Process a single strategy for a given timeframe with async optimization and return generated signals."""
        async with semaphore:
            try:
                logger.info(f"[STRATEGY] Evaluating strategy '{strategy_id}' for {timeframe} timeframe... 🔍")

                # 1. Evaluate entry conditions
                logger.info(f"[STRATEGY] Step 1: Evaluating entry conditions for {strategy_id}...")
                signal_df = await self._evaluate_strategy_conditions(strategy_id, strategy_def, data, timeframe)
                logger.info(f"[STRATEGY] Step 1 complete: Found {signal_df.height} signals for {strategy_id}")
                
                if signal_df.height == 0:
                    logger.info(f"[SIGNAL] No signals generated by strategy '{strategy_id}' in {timeframe}. 🤷")
                    return pl.DataFrame()

                # 2. Apply AI-Driven Signal Prediction (Feature 2)
                if self.config['model_inference_enabled'] and self.ai_models:
                    signal_df = await self._apply_ai_predictions(signal_df, timeframe)
                else:
                    logger.warning("[AI] AI model inference is disabled or no models loaded. Skipping AI predictions. 🤖")

                # 3. Calculate Signal Confidence Scoring (Feature 4)
                signal_df = await self._calculate_confidence_score(signal_df, strategy_id, timeframe)

                # 4. Apply Market Regime-Aware Signal Filtering (Feature 3)
                signal_df = await self._apply_regime_filtering(signal_df, market_regime)

                # 5. Apply Risk-Weighted Signal Output (Feature 6)
                risk_guidelines = await self._get_risk_management_guidelines()
                signal_df = await self._apply_risk_filtering(signal_df, risk_guidelines)

                if signal_df.height > 0:
                    logger.info(f"[SIGNAL] Generated {signal_df.height} final signals for strategy '{strategy_id}' in {timeframe} 🎉")
                    
                    # Generate Natural Language Summary for the first signal (Feature 10)
                    first_signal = signal_df.row(0, named=True)
                    summary = await self._generate_natural_language_summary(first_signal)
                    logger.info(f"[SUMMARY] Example Signal Summary: {summary} 🗣️")
                    
                    return signal_df # Return the signals for multi-timeframe consolidation
                else:
                    logger.info(f"[SIGNAL] All signals filtered out for strategy '{strategy_id}' in {timeframe}. 🗑️")
                    return pl.DataFrame()

            except Exception as e:
                logger.error(f"[ERROR] Failed to process strategy '{strategy_id}' for {timeframe}: {e}")
                return pl.DataFrame() # Ensure a DataFrame is always returned

    async def _evaluate_strategy_conditions(self, strategy_id: str, strategy_def: Dict[str, Any],
                                           data: pl.DataFrame, timeframe: str) -> pl.DataFrame:
        """
        Evaluate strategy entry conditions and generate signals.
        This is the core method that was missing and causing 0 signals.
        """
        try:
            logger.info(f"[STRATEGY] Evaluating conditions for {strategy_id} in {timeframe}...")

            if data.height == 0:
                logger.warning(f"[STRATEGY] No data available for {strategy_id} in {timeframe}")
                return pl.DataFrame()

            # Get strategy parameters
            parameters = strategy_def.get('parameters', {})
            entry_conditions = parameters.get('entry_conditions', [])

            if not entry_conditions:
                logger.info(f"[STRATEGY] No entry conditions defined for {strategy_id} - generating simple signals")
                # For strategies with no conditions, generate signals from all data
                signals = await self._generate_signals_from_data(strategy_id, strategy_def, data, timeframe)
                logger.info(f"[STRATEGY] Generated {signals.height} simple signals for {strategy_id} in {timeframe}")
                return signals

            # Calculate technical indicators needed for conditions
            data_with_indicators = await self._calculate_technical_indicators(data, timeframe)

            if data_with_indicators.height == 0:
                logger.warning(f"[STRATEGY] Failed to calculate indicators for {strategy_id}")
                return pl.DataFrame()

            # Evaluate each entry condition
            condition_results = []
            for condition in entry_conditions:
                condition_met = await self._evaluate_single_condition(condition, data_with_indicators)
                condition_results.append(condition_met)
                logger.debug(f"[CONDITION] {condition}: {condition_met.sum()} rows meet condition")

            # Combine all conditions (AND logic)
            if condition_results:
                final_condition = condition_results[0]
                for condition in condition_results[1:]:
                    final_condition = final_condition & condition

                # Filter data where all conditions are met
                signal_data = data_with_indicators.filter(final_condition)

                if signal_data.height > 0:
                    # Generate signals from the filtered data
                    signals = await self._generate_signals_from_data(strategy_id, strategy_def, signal_data, timeframe)
                    logger.info(f"[STRATEGY] Generated {signals.height} signals for {strategy_id} in {timeframe}")
                    return signals
                else:
                    logger.info(f"[STRATEGY] No data points meet all conditions for {strategy_id} in {timeframe}")
                    return pl.DataFrame()
            else:
                logger.warning(f"[STRATEGY] No valid conditions evaluated for {strategy_id}")
                return pl.DataFrame()

        except Exception as e:
            logger.error(f"[ERROR] Failed to evaluate strategy conditions for {strategy_id}: {e}")
            return pl.DataFrame()

    async def _calculate_technical_indicators(self, data: pl.DataFrame, timeframe: str) -> pl.DataFrame:
        """Calculate technical indicators needed for strategy conditions."""
        try:
            # Ensure we have required columns
            required_cols = ['timestamp', 'close', 'high', 'low', 'volume']
            missing_cols = [col for col in required_cols if col not in data.columns]
            if missing_cols:
                logger.warning(f"[INDICATORS] Missing columns: {missing_cols}")
                return data

            # Sort by timestamp
            data = data.sort('timestamp')

            # Calculate RSI
            data = data.with_columns([
                # Price change
                (pl.col('close') - pl.col('close').shift(1)).alias('price_change'),
            ])

            # Calculate RSI (simplified)
            window = 14
            data = data.with_columns([
                pl.when(pl.col('price_change') > 0)
                  .then(pl.col('price_change'))
                  .otherwise(0)
                  .rolling_mean(window)
                  .alias('avg_gain'),
                pl.when(pl.col('price_change') < 0)
                  .then(-pl.col('price_change'))
                  .otherwise(0)
                  .rolling_mean(window)
                  .alias('avg_loss'),
            ]).with_columns([
                (100 - (100 / (1 + (pl.col('avg_gain') / pl.col('avg_loss'))))).alias('rsi_14')
            ])

            # Calculate EMAs
            data = data.with_columns([
                pl.col('close').ewm_mean(span=20).alias('ema_20'),
                pl.col('close').ewm_mean(span=50).alias('ema_50'),
                pl.col('volume').rolling_mean(20).alias('avg_volume_20'),
            ])

            # Calculate additional indicators
            data = data.with_columns([
                # Price above/below EMA conditions
                (pl.col('close') > pl.col('ema_20')).alias('underlying_above_ema_20'),
                (pl.col('close') < pl.col('ema_20')).alias('underlying_below_ema_20'),
                # Volume conditions
                (pl.col('volume') > pl.col('avg_volume_20') * 1.2).alias('volume_above_avg'),
                # IV rank placeholder (would need options data)
                pl.lit(30.0).alias('iv_rank'),  # Placeholder
            ])

            return data

        except Exception as e:
            logger.error(f"[ERROR] Failed to calculate technical indicators: {e}")
            return data

    async def _evaluate_single_condition(self, condition: str, data: pl.DataFrame) -> pl.Series:
        """Evaluate a single entry condition and return boolean series."""
        try:
            condition = condition.strip()
            logger.debug(f"[CONDITION] Evaluating: {condition}")

            # Parse different condition types
            if "rsi_14 < " in condition:
                threshold = float(condition.split("< ")[1])
                return data['rsi_14'] < threshold
            elif "rsi_14 > " in condition:
                threshold = float(condition.split("> ")[1])
                return data['rsi_14'] > threshold
            elif "underlying_above_ema_20" in condition:
                return data['underlying_above_ema_20']
            elif "underlying_below_ema_20" in condition:
                return data['underlying_below_ema_20']
            elif "iv_rank < " in condition:
                threshold = float(condition.split("< ")[1])
                return data['iv_rank'] < threshold
            elif "iv_rank > " in condition:
                threshold = float(condition.split("> ")[1])
                return data['iv_rank'] > threshold
            elif "volume > avg_volume_20" in condition:
                return data['volume_above_avg']
            else:
                logger.warning(f"[CONDITION] Unknown condition format: {condition}")
                return pl.Series([True] * data.height)  # Default to True for unknown conditions

        except Exception as e:
            logger.error(f"[ERROR] Failed to evaluate condition '{condition}': {e}")
            return pl.Series([False] * data.height)

    async def _generate_signals_from_data(self, strategy_id: str, strategy_def: Dict[str, Any],
                                        signal_data: pl.DataFrame, timeframe: str) -> pl.DataFrame:
        """Generate trading signals from filtered data."""
        try:
            if signal_data.height == 0:
                return pl.DataFrame()

            # Get strategy parameters
            parameters = strategy_def.get('parameters', {})
            market_outlook = strategy_def.get('market_outlook', 'neutral')

            # Determine action based on strategy type and market outlook
            if 'long_call' in strategy_id.lower() or market_outlook == 'bullish':
                action = 'BUY_CE'
                option_type = 'CE'
            elif 'long_put' in strategy_id.lower() or market_outlook == 'bearish':
                action = 'BUY_PE'
                option_type = 'PE'
            else:
                action = 'BUY_CE'  # Default
                option_type = 'CE'

            # Get the latest signal point (most recent)
            latest_signal = signal_data.tail(1)

            if latest_signal.height == 0:
                return pl.DataFrame()

            # Create signal DataFrame
            signals = pl.DataFrame({
                'timestamp': latest_signal['timestamp'],
                'strategy_id': [strategy_id],
                'underlying': ['BANKNIFTY'],  # Default to BANKNIFTY for now
                'action': [action],
                'option_type': [option_type],
                'strike_price': [0],  # Will be calculated later
                'expiry': [''],  # Will be set later
                'entry_price': [0.0],  # Will be calculated later
                'stoploss': [0.0],
                'target': [0.0],
                'lot_size': [1],
                'timeframe': [timeframe],
                'confidence_score': [0.7],  # Default confidence
                'ai_model_confidence': [0.5],  # Placeholder
            })

            return signals

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate signals from data: {e}")
            return pl.DataFrame()

    async def _apply_ai_predictions(self, signal_df: pl.DataFrame, timeframe: str) -> pl.DataFrame:
        """Apply AI model predictions to enhance signals."""
        try:
            if signal_df.height == 0:
                return signal_df

            logger.info(f"[AI] Applying AI predictions for {signal_df.height} signals in {timeframe} timeframe... 🤖")

            # For now, just add AI confidence scores
            # In a full implementation, this would use the loaded AI models
            signal_df = signal_df.with_columns([
                pl.lit(0.75).alias('ai_model_confidence'),  # Default AI confidence
                pl.lit('ai_enhanced').alias('ai_prediction_type')
            ])

            logger.info(f"[AI] Enhanced {signal_df.height} signals with AI predictions")
            return signal_df

        except Exception as e:
            logger.error(f"[ERROR] Failed to apply AI predictions: {e}")
            return signal_df

    async def _extract_underlying_data(self, options_data: pl.DataFrame, timeframe: str) -> pl.DataFrame:
        """Extract underlying price data from options data for technical analysis."""
        try:
            # Check if we have underlying column to identify the underlying asset
            if 'underlying' not in options_data.columns:
                logger.warning(f"[EXTRACT] No 'underlying' column found in {timeframe} data")
                return pl.DataFrame()

            # Get unique underlyings
            underlyings = options_data['underlying'].unique().to_list()

            underlying_data_list = []

            for underlying in underlyings:
                # Filter data for this underlying
                underlying_options = options_data.filter(pl.col('underlying') == underlying)

                if underlying_options.height == 0:
                    continue

                # Create synthetic underlying data from ATM options or use a representative option
                # For simplicity, we'll use the average of all option prices as a proxy for underlying movement
                # In a real system, you'd want actual underlying index data

                synthetic_underlying = underlying_options.group_by('timestamp').agg([
                    pl.col('close').mean().alias('close'),
                    pl.col('high').max().alias('high'),
                    pl.col('low').min().alias('low'),
                    pl.col('open').first().alias('open'),
                    pl.col('volume').sum().alias('volume'),
                ]).with_columns([
                    pl.lit(underlying).alias('underlying'),
                    pl.lit('INDEX').alias('symbol'),
                ]).sort('timestamp')

                underlying_data_list.append(synthetic_underlying)

            if underlying_data_list:
                combined_underlying = pl.concat(underlying_data_list)
                logger.info(f"[EXTRACT] Extracted {combined_underlying.height} underlying data points for {timeframe}")
                return combined_underlying
            else:
                logger.warning(f"[EXTRACT] No underlying data could be extracted for {timeframe}")
                return pl.DataFrame()

        except Exception as e:
            logger.error(f"[ERROR] Failed to extract underlying data: {e}")
            return pl.DataFrame()

    async def _calculate_indicators(self, data: pl.DataFrame, indicators: List[Dict[str, Any]]) -> pl.DataFrame:
        """
        Calculates technical indicators on the DataFrame using polars_talib.
        """
        df = data.clone() # Work on a clone to avoid modifying original DataFrame directly
        for indicator_def in indicators:
            indicator_type = indicator_def.get('type')
            params = indicator_def.get('params', {})
            output_col = indicator_def.get('output_col')

            if not output_col:
                logger.warning(f"Indicator definition missing 'output_col': {indicator_def}. Skipping. ⚠️")
                continue

            try:
                if indicator_type == 'RSI':
                    length = params.get('length', 14)
                    df = df.with_columns(pt.RSI(pl.col('close'), length=length).alias(output_col))
                elif indicator_type == 'EMA':
                    length = params.get('length', 20)
                    df = df.with_columns(pt.EMA(pl.col('close'), length=length).alias(output_col))
                elif indicator_type == 'SMA':
                    length = params.get('length', 20)
                    df = df.with_columns(pt.SMA(pl.col('close'), length=length).alias(output_col))
                elif indicator_type == 'MACD':
                    fastperiod = params.get('fastperiod', 12)
                    slowperiod = params.get('slowperiod', 26)
                    signalperiod = params.get('signalperiod', 9)
                    df = df.with_columns([
                        pt.MACD(pl.col('close'), fastperiod=fastperiod, slowperiod=slowperiod, signalperiod=signalperiod).alias(output_col),
                        pt.MACDSIGNAL(pl.col('close'), fastperiod=fastperiod, slowperiod=slowperiod, signalperiod=signalperiod).alias(f"{output_col}_signal"),
                        pt.MACDHIST(pl.col('close'), fastperiod=fastperiod, slowperiod=slowperiod, signalperiod=signalperiod).alias(f"{output_col}_hist")
                    ])
                # Add more indicators as needed
                else:
                    logger.warning(f"Unsupported indicator type: {indicator_type}. Skipping. ⚠️")
            except Exception as e:
                logger.error(f"[ERROR] Failed to calculate indicator {indicator_type} for column {output_col}: {e}")
        return df

    async def _prepare_features_for_ai(self, data: pl.DataFrame, timeframe: str) -> pl.DataFrame:
        """
        Prepares the DataFrame with features suitable for AI model inference.
        This is a placeholder and should be aligned with the actual features used in AI training.
        """
        logger.info(f"[AI] Preparing features for AI models for {timeframe} data... 🧠")
        # Example features:
        # - Price changes, volatility, volume, Greeks, technical indicators
        # - Lagged features, rolling statistics
        
        # Ensure 'close' column exists for basic features
        if 'close' not in data.columns:
            logger.warning("[AI] 'close' column not found in data for AI feature preparation. Skipping. 🚫")
            return pl.DataFrame()

        features_df = data.with_columns([
            (pl.col('close').pct_change().alias('feature_price_change')),
            (pl.col('volume').log().alias('feature_log_volume')),
            (pl.col('close').rolling_mean(window_size=5).alias('feature_sma_5')),
            (pl.col('close').rolling_std(window_size=10).alias('feature_volatility_10')),
            # Add more relevant features based on your AI model's training
            # For options, you'd also include strike_price, expiry, option_type, implied_volatility, greeks etc.
            pl.col('strike_price').alias('feature_strike_price'),
            pl.col('expiry').cast(pl.Utf8).alias('feature_expiry_str'), # Convert to string for simplicity
            pl.col('option_type').cast(pl.Utf8).alias('feature_option_type_str'), # Convert to string for simplicity
            pl.col('delta').alias('feature_delta'),
            pl.col('gamma').alias('feature_gamma'),
            pl.col('theta').alias('feature_theta'),
            pl.col('vega').alias('feature_vega'),
        ]).fill_null(0) # Fill nulls for features that might appear due to shifts/rolling

        # Select only the feature columns that your AI model expects
        # This list should match the features used during model training
        expected_features = [
            'feature_price_change', 'feature_log_volume', 'feature_sma_5', 'feature_volatility_10',
            'feature_strike_price', 'feature_expiry_str', 'feature_option_type_str',
            'feature_delta', 'feature_gamma', 'feature_theta', 'feature_vega'
        ]
        
        # Filter for columns that actually exist in the DataFrame
        actual_features = [f for f in expected_features if f in features_df.columns]
        
        return features_df.select(actual_features)

    async def _predict_with_ai(self, signals_df: pl.DataFrame, timeframe: str) -> pl.DataFrame:
        """
        Uses trained AI models to predict trade direction, profitability, and ROI.
        Updates the signals DataFrame with these predictions.
        """
        if not self.ai_models:
            logger.warning("[AI] No AI models loaded. Skipping AI predictions. 🤖")
            return signals_df

        logger.info(f"[AI] Performing AI predictions for {timeframe} signals... 🔮")

        # Prepare features for AI models from the signals_df (which contains the original data row)
        # This assumes signals_df still contains the necessary raw data columns like 'close', 'volume', 'strike_price', etc.
        features_for_prediction = await self._prepare_features_for_ai(signals_df, timeframe)
        
        if features_for_prediction.height == 0:
            logger.warning("[AI] No features prepared for AI prediction. Skipping. 🚫")
            return signals_df

        # Convert Polars DataFrame to a format suitable for joblib-loaded models (e.g., NumPy array or list of dicts)
        # For simplicity, we'll convert to a list of dictionaries, which many models can handle.
        # In a real scenario, you'd convert to a NumPy array with specific feature order.
        features_list = features_for_prediction.to_dicts()

        # Placeholder for actual model prediction logic
        predicted_actions = []
        predicted_profitabilities = []
        predicted_rois = []
        predicted_confidences = [] # AI model's raw confidence/probability

        for features in features_list:
            # Example: Use a 'trade_direction_model' and 'profitability_model'
            # You would map model names to their specific prediction tasks
            trade_direction_model = self.ai_models.get(f"{timeframe}_trade_direction_model")
            profitability_model = self.ai_models.get(f"{timeframe}_profitability_model")
            roi_model = self.ai_models.get(f"{timeframe}_strategy_return_lightgbm") # Example model name

            current_features_array = pl.DataFrame([features]).to_numpy() # Convert single row to numpy array

            # Predict trade direction (e.g., 'BUY_CE', 'BUY_PE')
            if trade_direction_model:
                # Assuming a classification model that outputs probabilities
                proba = trade_direction_model.predict_proba(current_features_array)
                # Assuming proba is [[prob_buy_ce, prob_buy_pe, prob_skip]]
                if proba.shape[1] == 3:
                    action_idx = proba[0].argmax()
                    predicted_action = ['BUY_CE', 'BUY_PE', 'SKIP'][action_idx]
                    ai_confidence = proba[0][action_idx]
                else:
                    predicted_action = 'SKIP'
                    ai_confidence = 0.5 # Default if model output is unexpected
                predicted_actions.append(predicted_action)
                predicted_confidences.append(ai_confidence)
            else:
                predicted_actions.append(None)
                predicted_confidences.append(0.5) # Default confidence

            # Predict profitability (binary: 0 or 1)
            if profitability_model:
                predicted_profitability = profitability_model.predict(current_features_array)[0]
                predicted_profitabilities.append(predicted_profitability)
            else:
                predicted_profitabilities.append(None)

            # Estimate expected ROI
            if roi_model:
                predicted_roi = roi_model.predict(current_features_array)[0]
                predicted_rois.append(predicted_roi)
            else:
                predicted_rois.append(None)

        # Add predictions back to the signals DataFrame
        signals_df = signals_df.with_columns([
            pl.Series(name="ai_predicted_action", values=predicted_actions),
            pl.Series(name="ai_predicted_profitability", values=predicted_profitabilities),
            pl.Series(name="ai_predicted_roi", values=predicted_rois),
            pl.Series(name="ai_model_confidence", values=predicted_confidences)
        ])
        return signals_df

    async def _calculate_confidence_score(self, signals_df: pl.DataFrame, strategy_id: str, timeframe: str) -> pl.DataFrame:
        """
        Calculates a composite confidence score for each signal.
        Factors: AI model probability, Strategy historical Sharpe & ROI, Real-time conditions.
        """
        logger.info(f"[CONFIDENCE] Calculating confidence scores for {strategy_id} in {timeframe}... ⭐")

        # Placeholder for fetching historical strategy performance (from Backtesting Agent)
        # In a real system, this would involve reading from a database or a parquet file
        # e.g., from data/backtest/backtest_summary_*.json or data/ai_training/strategy_performance_*.parquet
        historical_performance = await self._get_strategy_historical_performance(strategy_id, timeframe)
        
        # Placeholder for real-time conditions (slippage, spread, VIX spike)
        real_time_conditions = await self._get_real_time_conditions()

        # Calculate confidence score
        # This is a simplified example. A more complex weighted average or ML model could be used.

        # Give simple strategies higher base confidence
        base_confidence = 0.8 if 'simple' in strategy_id.lower() else 0.5

        signals_with_confidence = signals_df.with_columns([
            # Start with AI model confidence (if available, otherwise default)
            pl.col("ai_model_confidence").fill_null(base_confidence).alias("base_confidence"),
            
            # Adjust based on historical Sharpe Ratio
            pl.lit(historical_performance.get('sharpe_ratio', 0.0)).alias("historical_sharpe"),
            # Adjust based on historical ROI
            pl.lit(historical_performance.get('avg_roi', 0.0)).alias("historical_roi"),
            
            # Adjust based on real-time conditions (simplified)
            pl.lit(real_time_conditions.get('slippage_factor', 1.0)).alias("slippage_factor"),
            pl.lit(real_time_conditions.get('spread_factor', 1.0)).alias("spread_factor"),
            pl.lit(real_time_conditions.get('vix_spike_factor', 1.0)).alias("vix_spike_factor"),
        ]).with_columns([
            # Combine factors into a final confidence score
            # Example formula: base_confidence * (1 + historical_sharpe_normalized) * (1 + historical_roi_normalized) * real_time_factors
            # Normalization of Sharpe/ROI would be needed to keep values within [0,1] or a reasonable range
            (
                pl.col("base_confidence") * 
                (1 + pl.col("historical_sharpe").clip(0, 2) * 0.1) + # Small adjustment for Sharpe
                (pl.col("historical_roi").clip(0, 100) * 0.001) # Small adjustment for ROI
            ).clip(0, 1).alias("confidence_score_raw") # Ensure it's between 0 and 1
        ]).with_columns([
            # Apply real-time condition adjustments
            (pl.col("confidence_score_raw") * pl.col("slippage_factor") * pl.col("spread_factor") * pl.col("vix_spike_factor"))
            .clip(0, 1).alias("confidence_score")
        ])

        # Update the original 'confidence_score' column in the signal
        signals_df = signals_with_confidence.select(signals_df.columns).with_columns(
            pl.col("confidence_score").alias("confidence_score")
        )
        return signals_df

    async def _get_strategy_historical_performance(self, strategy_id: str, timeframe: str) -> Dict[str, Any]:
        """
        Fetches historical performance metrics for a given strategy from the Backtesting Agent's data.
        This is a placeholder.
        """
        logger.info(f"[BACKTEST] Fetching historical performance for strategy '{strategy_id}' ({timeframe})... 📊")
        # In a real implementation, you would load the relevant backtest summary file
        # e.g., from data/backtest/backtest_summary_*.json
        # For now, return dummy data
        return {
            'sharpe_ratio': 1.2,
            'avg_roi': 15.5,
            'win_rate': 0.65,
            'total_trades': 100
        }

    async def _get_real_time_conditions(self) -> Dict[str, Any]:
        """
        Fetches real-time market conditions like slippage, spread, VIX spike.
        This is a placeholder.
        """
        logger.info("[MARKET] Fetching real-time market conditions... ⚡")
        # In a real implementation, this would query live market data or a monitoring agent
        # For now, return dummy data
        return {
            'slippage_factor': 0.98, # 2% negative impact
            'spread_factor': 0.99,   # 1% negative impact
            'vix_spike_factor': 1.0  # No VIX spike impact
        }

    async def _get_market_regime_info(self) -> Dict[str, Any]:
        """
        Fetches current market regime information from the Market Monitoring Agent.
        This is a placeholder.
        """
        logger.info("[REGIME] Fetching current market regime information... 🌍")
        # In a real implementation, this would query the Market Monitoring Agent
        # For now, return dummy data
        return {
            'trend': 'bullish', # 'bullish', 'bearish', 'sideways'
            'iv_regime': 'low', # 'low', 'medium', 'high'
            'active_strategies': [
                'simple_call_signal', 'simple_put_signal', 'volatility_breakout_ce', 'volatility_breakout_pe',
                'momentum_long_call', 'momentum_long_put', 'unusual_volume_ce', 'unusual_volume_pe',
                'trend_following_ce', 'trend_following_pe', 'oversold_bounce', 'overbought_fade'
            ], # All strategies are active for now
            'incompatible_trends': ['bearish'],
            'incompatible_iv_regimes': ['high']
        }

    async def _apply_regime_filtering(self, signals_df: pl.DataFrame, market_regime: Dict[str, Any]) -> pl.DataFrame:
        """
        Filters signals based on market regime compatibility.
        Also flags signals with "low-confidence-due-to-regime-mismatch".
        """
        logger.info("[REGIME] Applying market regime filtering... 🚦")
        if signals_df.height == 0:
            return signals_df

        current_trend = market_regime.get('trend')
        current_iv_regime = market_regime.get('iv_regime')
        active_strategies = set(market_regime.get('active_strategies', []))

        filtered_signals = []
        for signal in signals_df.iter_rows(named=True):
            strategy_id = signal.get('strategy_id')
            signal_action = signal.get('action')
            
            # Check if strategy is active for current regime
            if strategy_id not in active_strategies:
                signal['confidence_score'] *= 0.5 # Reduce confidence if strategy is not explicitly active
                signal['regime_mismatch_flag'] = "Strategy not active for current regime"
                logger.debug(f"Signal {strategy_id} flagged for inactive strategy.")
                # Decide if you want to completely reject or just reduce confidence
                # For now, we'll just reduce confidence.
            
            # Check for trend compatibility (simplified logic)
            if current_trend == 'bullish' and signal_action == 'BUY_PE':
                signal['confidence_score'] *= 0.3 # Significantly reduce confidence for counter-trend
                signal['regime_mismatch_flag'] = "Counter-trend signal (Bullish market, Buy PE)"
                logger.debug(f"Signal {strategy_id} flagged for counter-trend.")
            elif current_trend == 'bearish' and signal_action == 'BUY_CE':
                signal['confidence_score'] *= 0.3 # Significantly reduce confidence for counter-trend
                signal['regime_mismatch_flag'] = "Counter-trend signal (Bearish market, Buy CE)"
                logger.debug(f"Signal {strategy_id} flagged for counter-trend.")

            # Check for IV regime compatibility (simplified logic)
            # If IV is high, certain strategies (e.g., long calls/puts) might be less favorable
            # This would depend on the strategy definition itself.
            # For now, a generic reduction if IV regime is 'high' and it's a simple directional buy
            if current_iv_regime == 'high' and (signal_action == 'BUY_CE' or signal_action == 'BUY_PE'):
                signal['confidence_score'] *= 0.7 # Reduce confidence due to high IV
                signal['regime_mismatch_flag'] = "High IV regime for directional signal"
                logger.debug(f"Signal {strategy_id} flagged for high IV.")

            # Final filter based on confidence threshold after all adjustments
            if signal['confidence_score'] >= self.config['confidence_threshold']:
                filtered_signals.append(signal)
            else:
                logger.info(f"[FILTER] Signal {strategy_id} filtered out due to low confidence ({signal['confidence_score']:.2f}) after regime check. 📉")

        return pl.DataFrame(filtered_signals) if filtered_signals else pl.DataFrame()

    async def _get_risk_management_guidelines(self) -> Dict[str, Any]:
        """
        Fetches risk management guidelines from the Risk Management Agent.
        This is a placeholder.
        """
        logger.info("[RISK] Fetching risk management guidelines... 🛡️")
        # In a real implementation, this would query the Risk Management Agent
        # For now, return dummy data
        return {
            'max_daily_risk_pct': 2.0, # Max 2% of capital at risk per day
            'current_capital_at_risk_pct': 0.5, # Current capital at risk
            'max_trades_per_day': 5,
            'current_trades_today': 2,
            'block_new_trades': False # Override from Risk Agent
        }

    async def _apply_risk_filtering(self, signals_df: pl.DataFrame, risk_guidelines: Dict[str, Any]) -> pl.DataFrame:
        """
        Applies risk management guidelines to filter or adjust signals.
        """
        logger.info("[RISK] Applying risk management filtering... 🚨")
        if signals_df.height == 0:
            return signals_df

        max_daily_risk_pct = risk_guidelines.get('max_daily_risk_pct', 100.0)
        current_capital_at_risk_pct = risk_guidelines.get('current_capital_at_risk_pct', 0.0)
        max_trades_per_day = risk_guidelines.get('max_trades_per_day', 999)
        current_trades_today = risk_guidelines.get('current_trades_today', 0)
        block_new_trades = risk_guidelines.get('block_new_trades', False)

        if block_new_trades:
            logger.warning("[RISK] Risk Management Agent has blocked new trades. All signals skipped. 🛑")
            return pl.DataFrame()

        filtered_signals = []
        for signal in signals_df.iter_rows(named=True):
            signal_capital_at_risk = signal.get('capital_at_risk', 0.0)
            
            # Check if adding this signal breaches max daily risk
            if (current_capital_at_risk_pct + signal_capital_at_risk) > max_daily_risk_pct:
                logger.warning(f"[RISK] Signal {signal.get('strategy_id')} skipped: Max daily risk breached. 🚫")
                signal['is_cancelled'] = True # Mark as cancelled due to risk
                signal['cancellation_reason'] = "Max daily risk breached"
                continue
            
            # Check max trades per day
            if current_trades_today >= max_trades_per_day:
                logger.warning(f"[RISK] Signal {signal.get('strategy_id')} skipped: Max trades per day reached. 🚫")
                signal['is_cancelled'] = True
                signal['cancellation_reason'] = "Max trades per day reached"
                continue

            # Adjust signal sizing or other parameters if needed (placeholder)
            # For example, if risk is high but not breached, reduce lot size
            # signal['lot_size'] = min(signal['lot_size'], calculated_max_lot_size)

            filtered_signals.append(signal)
        
        return pl.DataFrame(filtered_signals) if filtered_signals else pl.DataFrame()

    async def _generate_natural_language_summary(self, signal: Dict[str, Any]) -> str:
        """
        Generates a natural language summary for a given signal (Feature 10).
        """
        logger.info("[LLM] Generating natural language signal summary... 📝")
        underlying = signal.get('underlying', 'UNKNOWN')
        strike_price = signal.get('strike_price', 'N/A')
        option_type = signal.get('option_type', 'N/A')
        action = signal.get('action', 'SKIP').replace('_', ' ')
        confidence_score = signal.get('confidence_score', 0.0)
        strategy_id = signal.get('strategy_id', 'UNKNOWN')
        features_triggered = signal.get('features_triggered', [])
        expected_roi = signal.get('expected_roi')
        risk_reward_ratio = signal.get('risk_reward_ratio')

        confidence_desc = "low-confidence"
        if confidence_score >= 0.8:
            confidence_desc = "high-confidence"
        elif confidence_score >= 0.6:
            confidence_desc = "medium-confidence"

        feature_summary = ", ".join(features_triggered) if features_triggered else "various technical factors"

        summary = (
            f"{underlying} {strike_price} {option_type} is a {confidence_desc} {action} signal "
            f"due to {feature_summary}. Strategy '{strategy_id}' is active."
        )
        
        if expected_roi is not None:
            summary += f" Expected ROI: {expected_roi:.1f}%."
        if risk_reward_ratio is not None:
            summary += f" Risk/Reward: {risk_reward_ratio:.1f}."

        return summary

    async def _multi_timeframe_confirmation(self, signals_df: pl.DataFrame) -> pl.DataFrame:
        """
        Applies multi-timeframe confirmation logic to signals (Feature 5).
        Signals are enhanced if multiple timeframes align.
        """
        logger.info("[MULTI-TF] Applying multi-timeframe confirmation logic... 🕰️")
        if signals_df.height == 0:
            return pl.DataFrame()

        # Group signals by underlying, strike, option_type, and action to find alignment
        # This assumes that a signal for the same underlying/strike/option_type/action
        # but different timeframes should be considered for alignment.
        
        # Convert timestamp to a common base for grouping (e.g., minute or second)
        # For simplicity, we'll group by underlying, strike, option_type, action, and a rounded timestamp
        # A more robust solution might involve a sliding window or more sophisticated time alignment.
        
        # Ensure 'timestamp' is a datetime object for proper grouping
        if 'timestamp' in signals_df.columns and signals_df['timestamp'].dtype != pl.Datetime:
            signals_df = signals_df.with_columns(
                pl.col('timestamp').str.strptime(pl.Datetime, "%Y-%m-%d %H:%M").alias('timestamp_dt')
            )
        else:
            signals_df = signals_df.with_columns(pl.col('timestamp').alias('timestamp_dt')) # Assume it's already datetime or handle if missing

        # Group by key signal identifiers and a rounded timestamp (e.g., to the nearest 5 minutes)
        # This helps align signals that might be generated slightly differently across TFs but refer to the same event.
        # For simplicity, we'll group by the exact timestamp for now, assuming signals are generated at the same time.
        # A more advanced approach would involve a time window.
        
        # For multi-timeframe alignment, we need to consider signals that are "active" around the same time.
        # Let's simplify: if a signal for a specific underlying/action/strike appears in multiple timeframes,
        # we enhance its confidence.
        
        confirmed_signals = []
        
        # Group by unique signal identifiers (excluding timeframe)
        # and then iterate through these groups
        grouped_signals = signals_df.group_by(['underlying', 'action', 'strike_price', 'option_type', 'expiry', 'timestamp_dt']).agg(
            pl.col('timeframe').unique().alias('aligned_timeframes'),
            pl.col('confidence_score').mean().alias('avg_confidence'),
            pl.col('strategy_id').unique().alias('triggering_strategies'),
            pl.first('entry_price').alias('entry_price'),
            pl.first('stoploss').alias('stoploss'),
            pl.first('target').alias('target'),
            pl.first('expected_roi').alias('expected_roi'),
            pl.first('risk_reward_ratio').alias('risk_reward_ratio'),
            pl.first('capital_at_risk').alias('capital_at_risk'),
            pl.first('lot_size').alias('lot_size'),
            pl.first('sl_buffer').alias('sl_buffer'),
            pl.first('features_triggered').alias('features_triggered'),
            pl.first('signal_type').alias('signal_type'),
            pl.first('is_new').alias('is_new'),
            pl.first('is_updated').alias('is_updated'),
            pl.first('is_cancelled').alias('is_cancelled'),
            pl.first('is_triggered').alias('is_triggered'),
            pl.first('signal_version').alias('signal_version')
        )

        for row in grouped_signals.iter_rows(named=True):
            aligned_timeframes = row['aligned_timeframes']
            confidence_score = row['avg_confidence']
            
            # Enhance confidence if multiple timeframes align
            if len(aligned_timeframes) > 1:
                confidence_score = min(1.0, confidence_score * (1 + 0.1 * (len(aligned_timeframes) - 1))) # 10% boost per additional TF
                logger.info(f"[MULTI-TF] Signal for {row['underlying']} {row['action']} confirmed across {len(aligned_timeframes)} TFs. Confidence boosted to {confidence_score:.2f} ✨")
            
            # Reconstruct the signal with updated confidence and aligned timeframes
            signal = {
                "timestamp": row['timestamp_dt'].strftime("%Y-%m-%d %H:%M"),
                "underlying": row['underlying'],
                "signal_type": row['signal_type'],
                "action": row['action'],
                "strike_price": row['strike_price'],
                "option_type": row['option_type'],
                "expiry": row['expiry'],
                "entry_price": row['entry_price'],
                "stoploss": row['stoploss'],
                "target": row['target'],
                "timeframe": ", ".join(aligned_timeframes), # Indicate multiple timeframes
                "strategy_id": ", ".join(row['triggering_strategies']), # Indicate multiple strategies
                "confidence_score": confidence_score,
                "expected_roi": row['expected_roi'],
                "risk_reward_ratio": row['risk_reward_ratio'],
                "capital_at_risk": row['capital_at_risk'],
                "lot_size": row['lot_size'],
                "sl_buffer": row['sl_buffer'],
                "is_new": row['is_new'],
                "is_updated": row['is_updated'],
                "is_cancelled": row['is_cancelled'],
                "is_triggered": row['is_triggered'],
                "features_triggered": row['features_triggered'],
                "last_5_trades_outcome": [], # Still placeholder
                "live_vs_backtest_performance": {}, # Still placeholder
                "signal_version": row['signal_version']
            }
            confirmed_signals.append(signal)

        return pl.DataFrame(confirmed_signals) if confirmed_signals else pl.DataFrame()

    async def _accept_agent_overrides(self, signals_df: pl.DataFrame) -> pl.DataFrame:
        """
        Accepts overrides/suggestions from other agents (Feature 12).
        This is a placeholder for integration with a message queue or direct API calls.
        """
        logger.info("[COORDINATION] Checking for multi-agent overrides/suggestions... 🤝")
        if signals_df.height == 0:
            return pl.DataFrame()

        # Example: Simulate an override from a "Risk Agent" or "Strategy Evolution Agent"
        # In a real system, this would be dynamic.
        
        # Override 1: Boost confidence for a specific strategy
        # Example: {"agent": "Strategy Evolution Agent", "type": "boost_confidence", "strategy_id": "strat_015", "boost_factor": 1.1}
        # Override 2: Block new trades for a period
        # Example: {"agent": "Risk Agent", "type": "block_trades", "duration_minutes": 30}
        
        # For demonstration, let's hardcode a few potential overrides
        mock_overrides = [
            # {"type": "boost_confidence", "strategy_id": "strat_017", "boost_factor": 1.1},
            # {"type": "block_trades", "reason": "High market volatility", "duration_minutes": 15}
        ]

        modified_signals = signals_df.clone()
        for override in mock_overrides:
            if override['type'] == 'boost_confidence':
                strategy_id_to_boost = override.get('strategy_id')
                boost_factor = override.get('boost_factor', 1.0)
                modified_signals = modified_signals.with_columns(
                    pl.when(pl.col('strategy_id').str.contains(strategy_id_to_boost)) # Use contains for multi-strategy_id
                    .then(pl.min(pl.lit(1.0), pl.col('confidence_score') * boost_factor))
                    .otherwise(pl.col('confidence_score'))
                    .alias('confidence_score')
                )
                logger.info(f"[COORDINATION] Applied confidence boost for strategy '{strategy_id_to_boost}' by {boost_factor}x. ⬆️")
            elif override['type'] == 'block_trades':
                reason = override.get('reason', 'Agent override')
                duration = override.get('duration_minutes', 0)
                logger.warning(f"[COORDINATION] All new trades blocked for {duration} minutes due to: {reason}. 🛑")
                # In a real system, this would set a global flag or communicate to the Execution Agent
                return pl.DataFrame() # Return empty DataFrame to block all signals

        return modified_signals

    async def _generate_signal_type_async(self, signal_type: str, timeframe: str, data: pl.LazyFrame):
        """Generate specific type of signal with async optimization"""
        # This method will be deprecated or refactored as strategies take over signal generation
        logger.warning(f"[DEPRECATED] _generate_signal_type_async is deprecated and will be removed. Signal generation is now strategy-driven. 🗑️")
        # The calls to this method will be removed from _run_signal_generation_cycle
        pass # No longer need to call the chunked methods directly from here

    async def _load_timeframe_data_lazy(self, timeframe: str) -> Optional[pl.LazyFrame]:
        """Load data for specific timeframe using lazy evaluation"""
        try:
            from pathlib import Path
            import os

            # Set environment variable to handle timezone parsing issues
            os.environ['POLARS_IGNORE_TIMEZONE_PARSE_ERROR'] = '1'

            data_path = Path(self.config['data_path'])

            # Try to load from different possible locations (prioritize live data)
            possible_locations = [
                data_path / "live" / timeframe,  # Live data has highest priority
                data_path / "historical" / timeframe,  # Historical data as fallback
                data_path / timeframe
            ]

            for location in possible_locations:
                if not location.exists():
                    continue

                # Try different file patterns
                patterns = [
                    f"*_{timeframe}_*.parquet",
                    f"*_{timeframe}.parquet",
                    f"historical_{timeframe}_*.parquet",
                    f"historical_{timeframe}.parquet",
                    f"*{timeframe}*.parquet"
                ]

                for pattern in patterns:
                    files = list(location.glob(pattern))
                    if files:
                        # Load the most recent file using lazy evaluation
                        latest_file = max(files, key=lambda x: x.stat().st_mtime)
                        logger.info(f"[LOAD] Loading {timeframe} data lazily from {latest_file} 📂")
                        return pl.scan_parquet(latest_file)

            logger.warning(f"[WARNING] No data found for {timeframe} timeframe. 🚫")
            return None

        except Exception as e:
            logger.error(f"[ERROR] Failed to load {timeframe} data: {e}")
            return None

    async def _generate_volatility_signals_chunked(self, timeframe: str, data: pl.LazyFrame):
        """Generate volatility-based signals with chunked processing"""
        logger.warning("[DEPRECATED] _generate_volatility_signals_chunked is deprecated. Use strategy-based signal generation. 🗑️")
        pass

    async def _generate_directional_signals_chunked(self, timeframe: str, data: pl.LazyFrame):
        """Generate directional signals with chunked processing"""
        logger.warning("[DEPRECATED] _generate_directional_signals_chunked is deprecated. Use strategy-based signal generation. 🗑️")
        pass
    
    async def _generate_flow_signals_chunked(self, timeframe: str, data: pl.LazyFrame):
        """Generate options flow signals with chunked processing"""
        logger.warning("[DEPRECATED] _generate_flow_signals_chunked is deprecated. Use strategy-based signal generation. 🗑️")
        pass

    async def _generate_greeks_signals_chunked(self, timeframe: str, data: pl.LazyFrame):
        """Generate Greeks-based signals with chunked processing"""
        logger.warning("[DEPRECATED] _generate_greeks_signals_chunked is deprecated. Use strategy-based signal generation. 🗑️")
        pass
    
    async def _calculate_iv_signals_optimized(self, options_data: pl.DataFrame, timeframe: str) -> pl.DataFrame:
        """Calculate implied volatility signals with optimized polars operations"""
        logger.warning("[DEPRECATED] _calculate_iv_signals_optimized is deprecated. Use strategy-based signal generation. 🗑️")
        return pl.DataFrame()

    async def _calculate_directional_signals_optimized(self, index_data: pl.DataFrame, timeframe: str) -> pl.DataFrame:
        """Calculate directional signals with optimized polars operations"""
        logger.warning("[DEPRECATED] _calculate_directional_signals_optimized is deprecated. Use strategy-based signal generation. 🗑️")
        return pl.DataFrame()

    async def _calculate_flow_signals_optimized(self, options_data: pl.DataFrame, timeframe: str) -> pl.DataFrame:
        """Calculate options flow signals with optimized polars operations"""
        logger.warning("[DEPRECATED] _calculate_flow_signals_optimized is deprecated. Use strategy-based signal generation. 🗑️")
        return pl.DataFrame()

    async def _calculate_greeks_optimized(self, options_data: pl.DataFrame) -> pl.DataFrame:
        """Calculate Greeks for options data"""
        try:
            import py_vollib.black_scholes.greeks.analytical as greeks
            from datetime import datetime, timedelta

            logger.info("[GREEKS] Calculating Greeks for options... 📊")

            # Add Greeks columns
            greeks_data = []
            risk_free_rate = 0.06  # 6% risk-free rate

            # Use simplified Greeks calculations for performance
            # In production, you would use proper Black-Scholes calculations
            greeks_data = options_data.with_columns([
                # Simplified Delta calculation (price sensitivity)
                pl.when(pl.col('option_type') == 'CE')
                  .then(0.5 + (pl.col('close') - pl.col('strike_price')) / pl.col('strike_price') * 0.1)
                  .otherwise(0.5 - (pl.col('strike_price') - pl.col('close')) / pl.col('strike_price') * 0.1)
                  .clip(0, 1).alias('delta'),

                # Simplified Gamma calculation (delta sensitivity)
                (1 / (pl.col('strike_price') * 0.2)).alias('gamma'),

                # Simplified Theta calculation (time decay)
                pl.when(pl.col('option_type') == 'CE')
                  .then(-pl.col('close') * 0.01)
                  .otherwise(-pl.col('close') * 0.008).alias('theta'),

                # Simplified Vega calculation (volatility sensitivity)
                (pl.col('strike_price') * 0.1).alias('vega')
            ])

            return greeks_data

        except Exception as e:
            logger.error(f"[ERROR] Optimized Greeks calculation failed: {e}")
            return options_data

    async def _calculate_greeks_signals_optimized(self, options_data: pl.DataFrame, timeframe: str) -> pl.DataFrame:
        """Calculate Greeks-based signals with optimized polars operations"""
        logger.warning("[DEPRECATED] _calculate_greeks_signals_optimized is deprecated. Use strategy-based signal generation. 🗑️")
        return pl.DataFrame()

    async def _calculate_iv_signals(self, options_data: pl.DataFrame, timeframe: str) -> pl.DataFrame:
        """Calculate implied volatility signals"""
        logger.warning("[DEPRECATED] _calculate_iv_signals is deprecated. Use strategy-based signal generation. 🗑️")
        return pl.DataFrame()

    async def _calculate_directional_signals(self, index_data: pl.DataFrame, timeframe: str) -> pl.DataFrame:
        """Calculate directional signals from index data"""
        logger.warning("[DEPRECATED] _calculate_directional_signals is deprecated. Use strategy-based signal generation. 🗑️")
        return pl.DataFrame()

    async def _calculate_flow_signals(self, options_data: pl.DataFrame, timeframe: str) -> pl.DataFrame:
        """Calculate options flow signals"""
        logger.warning("[DEPRECATED] _calculate_flow_signals is deprecated. Use strategy-based signal generation. 🗑️")
        return pl.DataFrame()

    async def _calculate_greeks_signals(self, options_data: pl.DataFrame, timeframe: str) -> pl.DataFrame:
        """Calculate Greeks-based signals"""
        logger.warning("[DEPRECATED] _calculate_greeks_signals is deprecated. Use strategy-based signal generation. 🗑️")
        return pl.DataFrame()

    async def _save_signals(self, signal_type: str, timeframe: str, signals: pl.DataFrame):
        """Save generated signals"""
        try:
            if signals.height == 0:
                return

            from pathlib import Path
            from datetime import datetime

            # Create signals directory
            signals_path = Path(self.config['data_path']) / "signals"
            signals_path.mkdir(parents=True, exist_ok=True)

            # Save signals
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{signal_type}_{timeframe}_{timestamp}.parquet"
            filepath = signals_path / filename

            signals.write_parquet(filepath)
            logger.info(f"[SAVE] Saved {signals.height} {signal_type} signals for {timeframe} to {filepath} 💾")

        except Exception as e:
            logger.error(f"[ERROR] Failed to save {signal_type} signals for {timeframe}: {e}")

    async def cleanup(self):
        """Cleanup resources"""
        try:
            logger.info("[CLEANUP] Cleaning up Options Signal Generation Agent... 🧹")
            self.is_running = False
            logger.info("[SUCCESS] Options Signal Generation Agent cleaned up ✨")
        except Exception as e:
            logger.error(f"[ERROR] Cleanup failed: {e}")

# Example usage
async def main():
    agent = OptionsSignalGenerationAgent()
    try:
        await agent.initialize()
        await agent.start()
    except KeyboardInterrupt:
        logger.info("Agent interrupted by user 🛑")
    finally:
        await agent.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
