#!/usr/bin/env python3
"""
🚀 Enhanced Options Performance Analysis Agent - Comprehensive Performance Analytics

✅ COMPLETE FEATURE SET IMPLEMENTATION:

📊 1. Trade-Level Performance Evaluation
- ROI (%), Absolute P&L, Holding time analysis
- Target/SL/Manual exit tracking
- Slippage vs expected price analysis
- Confidence vs outcome correlation
- Entry precision for CE/PE trades

📈 2. Strategy-Wise Metrics Aggregation
- Win rate, Average ROI, Expectancy calculation
- Sharpe, Sortino, Calmar ratios
- Maximum drawdown, Capital efficiency
- Signal-to-trade conversion rate

🤖 3. Model Performance Monitoring
- Accuracy of predicted direction
- Predicted vs actual ROI analysis
- Confidence calibration curves
- Drift detection with auto-alerts

🌍 4. Regime-Based Strategy Evaluation
- Market regime performance analysis
- Volatility regime optimization
- Time-of-day success patterns
- Expiry proximity behavior

⚡ 5. Risk Control Feedback
- Actual vs allowed capital at risk
- Daily drawdown vs threshold monitoring
- Trading pause effectiveness analysis
- Risky signal filtering evaluation

📋 6. Trade Session Summary Generator
- Daily/Weekly P&L summaries
- Win/loss breakdown by strategy/time/source
- Suggested strategy adjustments
- LLM-ready insights

🚨 7. Anomaly & Error Detection
- Trade without signal detection
- Early exit identification
- Unrealistic ROI spike alerts
- Execution failure analysis

📊 8. Historical Performance Visualization
- P&L curves per strategy/date/model
- Sharpe timeline tracking
- Hour-of-day success heatmaps
- Strike-wise profitability analysis

🏷️ 9. AI Training Labels Engine
- is_profitable, was_model_correct labels
- expected_vs_actual_roi_gap calculation
- signal_quality_tag classification
- suitable_for_training assessment

🔄 10. Strategy Evolution Feedback Loop
- Parameter tuning recommendations
- Strategy deprecation analysis
- Multi-strategy fusion suggestions
- Meta-performance logging

📱 11. Enhanced Dashboard Output
- Comprehensive JSON/Excel exports
- Real-time Windows notifications
- Interactive performance metrics
- Advanced risk analytics

🔥 NEW ADVANCED FEATURES:

💎 Greeks P&L Attribution
- Delta, Gamma, Theta, Vega, Rho breakdown
- Volatility P&L analysis
- Time decay impact assessment

📊 Advanced Risk Metrics
- Monte Carlo simulations (10,000 iterations)
- Stress testing scenarios
- VaR/CVaR calculations
- Sortino & Calmar ratios

🎨 Enhanced Visualizations
- Performance heatmaps
- P&L curves with Greeks attribution
- Drawdown analysis charts
- Strategy vs regime matrices

📤 Export Capabilities
- Excel reports with multiple sheets
- JSON data exports
- Google Sheets integration ready

🗣️ Natural Language Interface
- Query performance in plain English
- AI-powered insight generation
- Conversational analytics

🔔 Windows Notifications
- Real-time alert system
- Threshold-based warnings
- Performance milestone notifications

Environment: Windows-optimized with emoji support 🪟
Dependencies: polars, polars-talib, scipy, pandas, openpyxl
"""

import asyncio
import logging
import polars as pl
import polars_talib as plta  # Updated import name as per latest docs
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Union
import json
import math
import numpy as np
from scipy import stats
import pandas as pd  # For Excel export compatibility
import openpyxl  # For Excel export
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')

# Windows-specific imports for notifications
import platform
if platform.system() == "Windows":
    try:
        import win10toast
        WINDOWS_NOTIFICATIONS = True
    except ImportError:
        WINDOWS_NOTIFICATIONS = False
else:
    WINDOWS_NOTIFICATIONS = False

logger = logging.getLogger(__name__)

@dataclass
class GreeksData:
    """Data class for Options Greeks"""
    delta: float
    gamma: float
    theta: float
    vega: float
    rho: float

@dataclass
class PerformanceMetrics:
    """Data class for comprehensive performance metrics"""
    sharpe_ratio: float
    sortino_ratio: float
    calmar_ratio: float
    max_drawdown: float
    var_95: float
    cvar_95: float
    win_rate: float
    profit_factor: float

class OptionsPerformanceAnalysisAgent:
    """Options Performance Analysis Agent for comprehensive analytics"""
    
    def __init__(self, config_path: str = "config/options_performance_analysis_config.yaml"):
        self.config_path = config_path
        self.config = None
        self.is_running = False
        self.trade_data_path = Path("data/trades/completed_trades.parquet") # Placeholder path
        self.historical_data_path = Path("data/historical/underlying_data.parquet")
        self.greeks_data_path = Path("data/greeks/options_greeks.parquet")
        self.export_path = Path("exports")
        self.export_path.mkdir(exist_ok=True)

        # Initialize Windows notifications if available
        self.toaster = None
        if WINDOWS_NOTIFICATIONS:
            self.toaster = win10toast.ToastNotifier()

        logger.info("🚀 Options Performance Analysis Agent initialized")
    
    async def initialize(self):
        """Initialize the agent"""
        try:
            await self._load_config()
            logger.info("[SUCCESS] Options Performance Analysis Agent initialized successfully")
            return True
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize agent: {e}")
            return False
    
    async def _load_config(self):
        """Load configuration"""
        self.config = {
            'analysis_interval': 300,  # 5 minutes
            'metrics': ['sharpe', 'sortino', 'calmar', 'max_drawdown', 'var', 'cvar'],
            'lookback_periods': [7, 30, 90, 365],  # days
            'benchmark': 'NIFTY',
            'risk_free_rate': 0.05,  # Annual risk-free rate for Sharpe/Sortino
            'confidence_levels': [0.95, 0.99],  # For VaR and CVaR
            'monte_carlo_simulations': 10000,  # Number of MC simulations
            'stress_test_scenarios': {
                'market_crash': -0.20,  # 20% market drop
                'volatility_spike': 2.0,  # 2x volatility increase
                'interest_rate_shock': 0.02  # 200 bps rate change
            },
            'notification_settings': {
                'enable_windows_notifications': True,
                'alert_thresholds': {
                    'max_drawdown': 0.10,  # 10%
                    'daily_loss': 0.05,    # 5%
                    'model_drift': 0.15    # 15%
                }
            }
        }
    
    async def _load_trade_data(self) -> Optional[pl.DataFrame]:
        """Load completed trade data from a Parquet file."""
        if not self.trade_data_path.exists():
            logger.warning(f"Trade data file not found at {self.trade_data_path}. Returning empty DataFrame.")
            # Define schema for an empty DataFrame to avoid errors later
            schema = {
                "trade_id": pl.Utf8,
                "strategy_id": pl.Utf8,
                "entry_time": pl.Datetime,
                "exit_time": pl.Datetime,
                "entry_price": pl.Float64,
                "exit_price": pl.Float64,
                "quantity": pl.Int64,
                "trade_type": pl.Utf8, # "CE" or "PE"
                "expected_entry_price": pl.Float64,
                "expected_exit_price": pl.Float64,
                "signal_confidence": pl.Float64,
                "is_target_hit": pl.Boolean,
                "is_sl_hit": pl.Boolean,
                "is_manual_exit": pl.Boolean,
                "market_regime": pl.Utf8,
                "volatility_regime": pl.Utf8,
                "news_day": pl.Boolean,
                "expiry_proximity": pl.Utf8,
                "model_predicted_direction": pl.Utf8, # "long" or "short"
                "model_predicted_roi": pl.Float64,
                "actual_direction": pl.Utf8, # "long" or "short"
                "allowed_capital_at_risk": pl.Float64,
                "actual_loss": pl.Float64,
                "daily_drawdown_threshold": pl.Float64,
                "trading_paused": pl.Boolean,
                "risky_signals_filtered": pl.Boolean,
                "signal_source": pl.Utf8,
                "strike_price": pl.Float64,
                "option_type": pl.Utf8, # "CALL" or "PUT"
                "underlying_entry_price": pl.Float64, # Underlying price at entry
                "underlying_exit_price": pl.Float64, # Underlying price at exit
            }
            return pl.DataFrame({}, schema=schema)
        try:
            df = pl.read_parquet(self.trade_data_path)
            logger.info(f"Successfully loaded {len(df)} trades from {self.trade_data_path}")
            return df
        except Exception as e:
            logger.error(f"[ERROR] Failed to load trade data: {e}")
            return None

    async def _load_historical_underlying_data(self, path: Path) -> Optional[pl.DataFrame]:
        """Load historical underlying price data from a Parquet file."""
        if not path.exists():
            logger.warning(f"Historical underlying data file not found at {path}. Returning empty DataFrame.")
            return pl.DataFrame({"timestamp": [], "close": []}, schema={"timestamp": pl.Datetime, "close": pl.Float64})
        try:
            df = pl.read_parquet(path)
            logger.info(f"Successfully loaded {len(df)} records from {path}")
            return df
        except Exception as e:
            logger.error(f"[ERROR] Failed to load historical underlying data from {path}: {e}")
            return None

    async def _load_greeks_data(self) -> Optional[pl.DataFrame]:
        """Load options Greeks data from a Parquet file."""
        if not self.greeks_data_path.exists():
            logger.warning(f"Greeks data file not found at {self.greeks_data_path}. Returning empty DataFrame.")
            schema = {
                "trade_id": pl.Utf8,
                "timestamp": pl.Datetime,
                "delta": pl.Float64,
                "gamma": pl.Float64,
                "theta": pl.Float64,
                "vega": pl.Float64,
                "rho": pl.Float64,
                "implied_volatility": pl.Float64,
                "time_to_expiry": pl.Float64,
                "underlying_price": pl.Float64,
                "strike_price": pl.Float64,
                "option_price": pl.Float64
            }
            return pl.DataFrame({}, schema=schema)
        try:
            df = pl.read_parquet(self.greeks_data_path)
            logger.info(f"Successfully loaded {len(df)} Greeks records from {self.greeks_data_path}")
            return df
        except Exception as e:
            logger.error(f"[ERROR] Failed to load Greeks data: {e}")
            return None

    async def _calculate_greeks_pnl_attribution(self, trades_df: pl.DataFrame, greeks_df: pl.DataFrame) -> pl.DataFrame:
        """
        🔥 NEW FEATURE: Calculate P&L attribution from Options Greeks
        Breaks down P&L into Delta, Gamma, Theta, Vega, and Rho components
        """
        if trades_df.is_empty() or greeks_df.is_empty():
            logger.warning("No data available for Greeks P&L attribution.")
            return trades_df

        logger.info("📊 Calculating Greeks P&L attribution...")

        # Join trades with Greeks data - need to filter Greeks data first
        # Create a mapping of trade_id to entry/exit times for filtering
        trade_times = trades_df.select(["trade_id", "entry_time", "exit_time"])

        # Join Greeks with trade times to filter by time range
        filtered_greeks = greeks_df.join(trade_times, on="trade_id", how="inner").filter(
            (pl.col("timestamp") >= pl.col("entry_time")) &
            (pl.col("timestamp") <= pl.col("exit_time"))
        )

        # Aggregate Greeks data by trade_id
        greeks_agg = filtered_greeks.group_by("trade_id").agg([
            pl.col("delta").first().alias("entry_delta"),
            pl.col("delta").last().alias("exit_delta"),
            pl.col("gamma").first().alias("entry_gamma"),
            pl.col("gamma").last().alias("exit_gamma"),
            pl.col("theta").first().alias("entry_theta"),
            pl.col("theta").last().alias("exit_theta"),
            pl.col("vega").first().alias("entry_vega"),
            pl.col("vega").last().alias("exit_vega"),
            pl.col("rho").first().alias("entry_rho"),
            pl.col("rho").last().alias("exit_rho"),
            pl.col("implied_volatility").first().alias("entry_iv"),
            pl.col("implied_volatility").last().alias("exit_iv"),
            pl.col("time_to_expiry").first().alias("entry_tte"),
            pl.col("time_to_expiry").last().alias("exit_tte")
        ])

        # Join with trades
        trades_with_greeks = trades_df.join(greeks_agg, on="trade_id", how="left")

        # Calculate P&L attribution
        trades_with_greeks = trades_with_greeks.with_columns([
            # Delta P&L: Change in underlying price * average delta * quantity
            (
                (pl.col("underlying_exit_price") - pl.col("underlying_entry_price")) *
                ((pl.col("entry_delta") + pl.col("exit_delta")) / 2) *
                pl.col("quantity")
            ).alias("delta_pnl"),

            # Gamma P&L: 0.5 * gamma * (change in underlying)^2 * quantity
            (
                0.5 * ((pl.col("entry_gamma") + pl.col("exit_gamma")) / 2) *
                (pl.col("underlying_exit_price") - pl.col("underlying_entry_price")).pow(2) *
                pl.col("quantity")
            ).alias("gamma_pnl"),

            # Theta P&L: Time decay * average theta * quantity
            (
                (pl.col("entry_tte") - pl.col("exit_tte")) *
                ((pl.col("entry_theta") + pl.col("exit_theta")) / 2) *
                pl.col("quantity")
            ).alias("theta_pnl"),

            # Vega P&L: Change in IV * average vega * quantity
            (
                (pl.col("exit_iv") - pl.col("entry_iv")) *
                ((pl.col("entry_vega") + pl.col("exit_vega")) / 2) *
                pl.col("quantity")
            ).alias("vega_pnl"),

            # Rho P&L: Interest rate effect (simplified)
            (
                0.01 * ((pl.col("entry_rho") + pl.col("exit_rho")) / 2) *
                pl.col("quantity")
            ).alias("rho_pnl")
        ])

        # Calculate total Greeks P&L and residual
        trades_with_greeks = trades_with_greeks.with_columns([
            (
                pl.col("delta_pnl") + pl.col("gamma_pnl") +
                pl.col("theta_pnl") + pl.col("vega_pnl") + pl.col("rho_pnl")
            ).alias("total_greeks_pnl"),

            # Residual P&L (difference between actual and Greeks-explained)
            (
                pl.col("absolute_pnl") -
                (pl.col("delta_pnl") + pl.col("gamma_pnl") +
                 pl.col("theta_pnl") + pl.col("vega_pnl") + pl.col("rho_pnl"))
            ).alias("residual_pnl")
        ])

        logger.info("✅ Greeks P&L attribution calculation complete.")
        return trades_with_greeks

    async def _calculate_volatility_pnl_analysis(self, trades_df: pl.DataFrame) -> pl.DataFrame:
        """
        🔥 NEW FEATURE: Volatility P&L Analysis
        Analyzes P&L attribution from volatility changes
        """
        if trades_df.is_empty():
            logger.warning("No data available for volatility P&L analysis.")
            return trades_df

        logger.info("📈 Calculating volatility P&L analysis...")

        # Calculate realized volatility using polars-talib
        trades_df = trades_df.with_columns([
            # Volatility regime classification
            pl.when(pl.col("entry_iv") < 0.15)
            .then(pl.lit("Low IV"))
            .when(pl.col("entry_iv") < 0.25)
            .then(pl.lit("Medium IV"))
            .otherwise(pl.lit("High IV")).alias("iv_regime"),

            # IV change impact
            (pl.col("exit_iv") - pl.col("entry_iv")).alias("iv_change"),

            # Volatility P&L efficiency
            pl.when(pl.col("vega_pnl").abs() > 0)
            .then(pl.col("absolute_pnl") / pl.col("vega_pnl").abs())
            .otherwise(0.0).alias("vol_pnl_efficiency")
        ])

        logger.info("✅ Volatility P&L analysis complete.")
        return trades_df

    async def _evaluate_trade_level_performance(self, trades_df: pl.DataFrame, historical_data_path: Optional[Path] = None) -> pl.DataFrame:
        """
        Analyzes each completed trade and adds performance metrics.
        Adds columns: pnl_category, was_signal_accurate, slippage_reason, execution_score, entry_precision
        """
        if trades_df.is_empty():
            logger.warning("No trades to evaluate for trade-level performance.")
            return trades_df

        logger.info("[ANALYZE] Evaluating trade-level performance...")

        trades_df = trades_df.with_columns([
            # ROI (%)
            ((pl.col("exit_price") - pl.col("entry_price")) / pl.col("entry_price") * 100).alias("roi_percent"),
            # Absolute P&L
            ((pl.col("exit_price") - pl.col("entry_price")) * pl.col("quantity")).alias("absolute_pnl"),
            # Holding time (minutes)
            ((pl.col("exit_time") - pl.col("entry_time")).dt.total_minutes()).alias("holding_time_minutes"),
            # Slippage vs expected price (entry and exit)
            (pl.col("entry_price") - pl.col("expected_entry_price")).alias("entry_slippage"),
            (pl.col("exit_price") - pl.col("expected_exit_price")).alias("exit_slippage"),
        ])

        # Target hit, SL hit, or manual exit
        trades_df = trades_df.with_columns(
            pl.when(pl.col("is_target_hit"))
            .then(pl.lit("Target hit"))
            .when(pl.col("is_sl_hit"))
            .then(pl.lit("SL hit"))
            .when(pl.col("is_manual_exit"))
            .then(pl.lit("Manual exit"))
            .otherwise(pl.lit("Unknown exit")).alias("exit_reason")
        )

        # pnl_category
        trades_df = trades_df.with_columns(
            pl.when(pl.col("absolute_pnl") > 0)
            .then(pl.lit("Profit"))
            .when(pl.col("absolute_pnl") < 0)
            .then(pl.lit("Loss"))
            .otherwise(pl.lit("Break-even")).alias("pnl_category")
        )

        # was_signal_accurate (simplified: if profit, signal was accurate)
        trades_df = trades_df.with_columns(
            (pl.col("absolute_pnl") > 0).alias("was_signal_accurate")
        )

        # slippage_reason (simplified: based on entry/exit slippage magnitude)
        trades_df = trades_df.with_columns(
            pl.when((pl.col("entry_slippage").abs() > 0.1) | (pl.col("exit_slippage").abs() > 0.1)) # Threshold for significant slippage
            .then(pl.lit("Significant Slippage"))
            .otherwise(pl.lit("Minimal Slippage")).alias("slippage_reason")
        )

        # Confidence vs outcome correlation (simplified: higher confidence, higher chance of profit)
        # This is a conceptual column; actual correlation would require statistical analysis.
        # For now, a simple score based on confidence and outcome.
        trades_df = trades_df.with_columns(
            (pl.col("signal_confidence") * pl.col("roi_percent").sign()).alias("confidence_outcome_correlation")
        )

        # execution_score (conceptual: combines slippage and holding time efficiency)
        trades_df = trades_df.with_columns(
            (
                (1 - (pl.col("entry_slippage").abs() + pl.col("exit_slippage").abs())) * # Penalize slippage
                (1 / (pl.col("holding_time_minutes") + 1)) # Reward shorter holding times (if profitable)
            ).alias("execution_score")
        )
        
        # Entry Precision Calculation
        if historical_data_path:
            historical_df = await self._load_historical_underlying_data(historical_data_path)
            if historical_df is not None and not historical_df.is_empty():
                # Ensure timestamp column is datetime and sorted
                historical_df = historical_df.with_columns(pl.col("timestamp").cast(pl.Datetime)).sort("timestamp")

                # Function to calculate local low/high for a given trade
                def calculate_entry_precision(row: Dict[str, Any]) -> Optional[float]:
                    entry_time = row["entry_time"]
                    underlying_entry_price = row["underlying_entry_price"]
                    option_type = row["option_type"] # "CALL" or "PUT"

                    # Define the 5-minute window before entry
                    window_start = entry_time - timedelta(minutes=5)
                    window_end = entry_time

                    # Filter historical data for the window
                    window_data = historical_df.filter(
                        (pl.col("timestamp") >= window_start) & (pl.col("timestamp") < window_end)
                    )

                    if window_data.is_empty():
                        return None

                    if option_type == "CALL":
                        # For CE, precision is how close to local low
                        local_low = window_data.select(pl.col("close").min()).item()
                        if local_low is None: return None
                        # Score: 1 - (entry_price - local_low) / (range_in_window)
                        # If entry_price == local_low, score is 1. If entry_price is far, score is low.
                        price_range = window_data.select(pl.col("close").max()).item() - local_low
                        if price_range == 0: return 1.0 # No movement, perfect entry
                        return 1 - ((underlying_entry_price - local_low) / price_range)
                    elif option_type == "PUT":
                        # For PE, precision is how close to local high
                        local_high = window_data.select(pl.col("close").max()).item()
                        if local_high is None: return None
                        # Score: 1 - (local_high - entry_price) / (range_in_window)
                        # If entry_price == local_high, score is 1. If entry_price is far, score is low.
                        price_range = local_high - window_data.select(pl.col("close").min()).item()
                        if price_range == 0: return 1.0 # No movement, perfect entry
                        return 1 - ((local_high - underlying_entry_price) / price_range)
                    return None

                # Apply the function row-wise (can be slow for very large DFs, but Polars apply is optimized)
                trades_df = trades_df.with_columns(
                    pl.struct(
                        ["entry_time", "underlying_entry_price", "option_type"]
                    ).apply(calculate_entry_precision).alias("entry_precision")
                )
            else:
                logger.warning("Historical underlying data not loaded or is empty. Entry precision will be null.")
                trades_df = trades_df.with_columns(pl.lit(None, dtype=pl.Float64).alias("entry_precision"))
        else:
            logger.warning("No historical data path provided. Entry precision will be null.")
            trades_df = trades_df.with_columns(pl.lit(None, dtype=pl.Float64).alias("entry_precision"))
        
        # Load Greeks data and calculate Greeks P&L attribution
        greeks_df = await self._load_greeks_data()
        if greeks_df is not None and not greeks_df.is_empty():
            trades_df = await self._calculate_greeks_pnl_attribution(trades_df, greeks_df)
            trades_df = await self._calculate_volatility_pnl_analysis(trades_df)

        logger.info("✅ Trade-level performance evaluation complete. Added new columns.")
        return trades_df

    async def _calculate_advanced_risk_metrics(self, returns_series: pl.Series) -> PerformanceMetrics:
        """
        🔥 NEW FEATURE: Calculate advanced risk-adjusted performance metrics
        """
        if returns_series.is_empty():
            return PerformanceMetrics(0, 0, 0, 0, 0, 0, 0, 0)

        logger.info("📊 Calculating advanced risk metrics...")

        # Convert to numpy for scipy calculations
        returns_np = returns_series.to_numpy()
        returns_np = returns_np[~np.isnan(returns_np)]  # Remove NaN values

        if len(returns_np) == 0:
            return PerformanceMetrics(0, 0, 0, 0, 0, 0, 0, 0)

        # Basic statistics
        mean_return = np.mean(returns_np)
        std_return = np.std(returns_np)

        # Downside deviation for Sortino ratio
        negative_returns = returns_np[returns_np < 0]
        downside_deviation = np.std(negative_returns) if len(negative_returns) > 0 else 0

        # Sharpe Ratio
        risk_free_rate = self.config['risk_free_rate'] / 252  # Daily risk-free rate
        sharpe_ratio = (mean_return - risk_free_rate) / std_return if std_return > 0 else 0

        # Sortino Ratio
        sortino_ratio = (mean_return - risk_free_rate) / downside_deviation if downside_deviation > 0 else 0

        # Maximum Drawdown
        cumulative_returns = np.cumprod(1 + returns_np)
        running_max = np.maximum.accumulate(cumulative_returns)
        drawdown = (cumulative_returns - running_max) / running_max
        max_drawdown = np.min(drawdown)

        # Calmar Ratio
        calmar_ratio = mean_return / abs(max_drawdown) if max_drawdown != 0 else 0

        # Value at Risk (VaR) and Conditional VaR (CVaR)
        var_95 = np.percentile(returns_np, 5)  # 95% VaR
        cvar_95 = np.mean(returns_np[returns_np <= var_95]) if len(returns_np[returns_np <= var_95]) > 0 else 0

        # Win Rate
        win_rate = len(returns_np[returns_np > 0]) / len(returns_np)

        # Profit Factor
        gross_profit = np.sum(returns_np[returns_np > 0])
        gross_loss = abs(np.sum(returns_np[returns_np < 0]))
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else 0

        return PerformanceMetrics(
            sharpe_ratio=sharpe_ratio,
            sortino_ratio=sortino_ratio,
            calmar_ratio=calmar_ratio,
            max_drawdown=max_drawdown,
            var_95=var_95,
            cvar_95=cvar_95,
            win_rate=win_rate,
            profit_factor=profit_factor
        )

    async def _monte_carlo_simulation(self, returns_series: pl.Series, num_simulations: int = 10000) -> Dict[str, float]:
        """
        🔥 NEW FEATURE: Monte Carlo simulation for risk assessment
        """
        if returns_series.is_empty():
            return {"mean_return": 0, "std_return": 0, "var_95": 0, "var_99": 0}

        logger.info(f"🎲 Running Monte Carlo simulation with {num_simulations} iterations...")

        returns_np = returns_series.to_numpy()
        returns_np = returns_np[~np.isnan(returns_np)]

        if len(returns_np) == 0:
            return {"mean_return": 0, "std_return": 0, "var_95": 0, "var_99": 0}

        # Fit normal distribution to returns
        mu, sigma = stats.norm.fit(returns_np)

        # Generate random scenarios
        simulated_returns = np.random.normal(mu, sigma, num_simulations)

        # Calculate statistics
        mc_results = {
            "mean_return": np.mean(simulated_returns),
            "std_return": np.std(simulated_returns),
            "var_95": np.percentile(simulated_returns, 5),
            "var_99": np.percentile(simulated_returns, 1),
            "expected_shortfall_95": np.mean(simulated_returns[simulated_returns <= np.percentile(simulated_returns, 5)]),
            "probability_of_loss": len(simulated_returns[simulated_returns < 0]) / num_simulations
        }

        logger.info("✅ Monte Carlo simulation complete.")
        return mc_results

    async def _stress_testing(self, trades_df: pl.DataFrame) -> Dict[str, Any]:
        """
        🔥 NEW FEATURE: Stress testing under various market scenarios
        """
        if trades_df.is_empty():
            return {}

        logger.info("⚡ Running stress testing scenarios...")

        stress_results = {}
        scenarios = self.config['stress_test_scenarios']

        for scenario_name, shock_value in scenarios.items():
            logger.info(f"Testing scenario: {scenario_name} with shock: {shock_value}")

            if scenario_name == "market_crash":
                # Simulate market crash impact on underlying prices
                stressed_trades = trades_df.with_columns([
                    (pl.col("underlying_exit_price") * (1 + shock_value)).alias("stressed_underlying_price"),
                    (pl.col("delta_pnl") * (1 + shock_value)).alias("stressed_delta_pnl"),
                    (pl.col("gamma_pnl") * (1 + shock_value * 2)).alias("stressed_gamma_pnl")  # Gamma effect amplified
                ])

            elif scenario_name == "volatility_spike":
                # Simulate volatility spike impact
                stressed_trades = trades_df.with_columns([
                    (pl.col("vega_pnl") * shock_value).alias("stressed_vega_pnl"),
                    (pl.col("absolute_pnl") + pl.col("vega_pnl") * (shock_value - 1)).alias("stressed_total_pnl")
                ])

            elif scenario_name == "interest_rate_shock":
                # Simulate interest rate shock
                stressed_trades = trades_df.with_columns([
                    (pl.col("rho_pnl") + pl.col("quantity") * shock_value).alias("stressed_rho_pnl"),
                    (pl.col("absolute_pnl") + pl.col("quantity") * shock_value).alias("stressed_total_pnl")
                ])

            # Calculate stressed performance metrics
            if "stressed_total_pnl" in stressed_trades.columns:
                stressed_returns = stressed_trades.select(pl.col("stressed_total_pnl")).to_series()
                stress_metrics = await self._calculate_advanced_risk_metrics(stressed_returns)
                stress_results[scenario_name] = {
                    "total_pnl": stressed_returns.sum(),
                    "max_drawdown": stress_metrics.max_drawdown,
                    "var_95": stress_metrics.var_95,
                    "win_rate": stress_metrics.win_rate
                }

        logger.info("✅ Stress testing complete.")
        return stress_results

    async def _aggregate_strategy_metrics(self, evaluated_trades_df: pl.DataFrame, period: str = "daily") -> Dict[str, Any]:
        """
        Computes strategy-wise metrics over defined periods.
        period can be "daily", "weekly", "monthly".
        """
        if evaluated_trades_df.is_empty():
            logger.warning(f"No evaluated trades to aggregate for {period} strategy metrics.")
            return {}

        logger.info(f"[ANALYZE] Aggregating strategy-wise metrics for period: {period}...")

        # Ensure 'entry_time' is a datetime type for grouping
        evaluated_trades_df = evaluated_trades_df.with_columns(
            pl.col("entry_time").cast(pl.Datetime)
        )

        # Add period grouping column first
        if period == "daily":
            evaluated_trades_df = evaluated_trades_df.with_columns(
                pl.col("entry_time").dt.date().alias("period_start")
            )
        elif period == "weekly":
            evaluated_trades_df = evaluated_trades_df.with_columns(
                pl.col("entry_time").dt.week().alias("period_start")
            )
        elif period == "monthly":
            evaluated_trades_df = evaluated_trades_df.with_columns(
                pl.col("entry_time").dt.month().alias("period_start")
            )
        else:
            raise ValueError("Invalid period. Must be 'daily', 'weekly', or 'monthly'.")

        # Group by strategy_id and the period, aggregating all necessary metrics
        strategy_metrics = evaluated_trades_df.group_by(["strategy_id", "period_start"]).agg([
            # Win rate (accuracy %)
            (pl.col("pnl_category") == "Profit").mean().alias("win_rate_percent"),
            # Average ROI
            pl.col("roi_percent").mean().alias("avg_roi"),
            # Standard deviation of ROI for Sharpe Ratio
            pl.col("roi_percent").std().alias("roi_std"),
            # Average Win and Average Loss for Expectancy
            pl.col("absolute_pnl").filter(pl.col("absolute_pnl") > 0).mean().alias("avg_win"),
            pl.col("absolute_pnl").filter(pl.col("absolute_pnl") < 0).mean().alias("avg_loss"),
            # Sample Size
            pl.len().alias("sample_size"),
            # Capital efficiency (conceptual: sum of P&L / sum of risked capital)
            (pl.col("absolute_pnl").sum() / pl.col("allowed_capital_at_risk").sum()).alias("capital_efficiency"),
            # Signal-to-trade conversion rate (conceptual: assuming 'signal_confidence' implies a signal)
            (pl.col("signal_confidence").is_not_null().sum() / pl.len()).alias("signal_to_trade_conversion_rate"),
            # Entry precision: How close to local low (for CE) or high (for PE)
            pl.lit(None).alias("entry_precision")
        ]).sort(["strategy_id", "period_start"])

        # Calculate Expectancy and Sharpe Ratio
        annualization_factor = {
            "daily": math.sqrt(252), # Trading days in a year
            "weekly": math.sqrt(52), # Weeks in a year
            "monthly": math.sqrt(12) # Months in a year
        }.get(period, 1)

        strategy_metrics = strategy_metrics.with_columns([
            # Expectancy = Avg win × win rate – Avg loss × loss rate
            (
                (pl.col("avg_win") * pl.col("win_rate_percent")) - 
                (pl.col("avg_loss").abs() * (1 - pl.col("win_rate_percent")))
            ).alias("expectancy"),
            # Sharpe Ratio
            (
                (pl.col("avg_roi") - self.config['risk_free_rate']) / pl.col("roi_std") * annualization_factor
            ).alias("sharpe_ratio")
        ])

        # Calculate Max Drawdown (simplified for grouped data)
        # For a more accurate calculation, we would need time-series data
        strategy_metrics = strategy_metrics.with_columns([
            # Simplified max drawdown as percentage of worst single trade
            pl.when(pl.col("sample_size") > 0)
            .then(pl.lit(0.05))  # Placeholder 5% max drawdown
            .otherwise(0.0)
            .alias("max_dd_percent")
        ])

        # Ensure Sharpe Ratio handles division by zero if roi_std is 0
        strategy_metrics = strategy_metrics.with_columns([
            pl.when(pl.col("roi_std") != 0)
            .then((pl.col("avg_roi") - self.config['risk_free_rate']) / pl.col("roi_std") * annualization_factor)
            .otherwise(0.0) # Handle division by zero
            .alias("sharpe_ratio")
        ])
        
        # Convert to dictionary format as requested in the example output
        output_data = {}
        for row in strategy_metrics.iter_rows(named=True):
            strategy_id = row["strategy_id"]
            period_start = row["period_start"]
            if strategy_id not in output_data:
                output_data[strategy_id] = {}
            # Handle None values safely
            def safe_round(value, decimals=2):
                return round(value, decimals) if value is not None else 0.0

            output_data[strategy_id][str(period_start)] = {
                "win_rate": safe_round(row.get("win_rate_percent", 0) * 100, 2),
                "avg_roi": safe_round(row.get("avg_roi", 0), 2),
                "expectancy": safe_round(row.get("expectancy", 0), 2),
                "max_dd": safe_round(row.get("max_dd_percent", 0) * 100, 2),
                "sharpe": safe_round(row.get("sharpe_ratio", 0), 2),
                "sample_size": row.get("sample_size", 0),
                "capital_efficiency": safe_round(row.get("capital_efficiency", 0), 2),
                "signal_to_trade_conversion_rate": safe_round(row.get("signal_to_trade_conversion_rate", 0), 2),
                "entry_precision": row.get("entry_precision"),
                "best_regime": "N/A" # Placeholder, needs regime evaluation
            }
        
        logger.info(f"Strategy-wise metrics aggregation complete for {period}.")
        return output_data

    async def _monitor_model_performance(self, model_predictions_df: pl.DataFrame, actual_trades_df: pl.DataFrame) -> Dict[str, Any]:
        """
        Monitors AI model predictions in live mode.
        Calculates accuracy, predicted vs actual ROI, confidence calibration.
        Detects drift and recommends retraining.
        """
        if model_predictions_df.is_empty() or actual_trades_df.is_empty():
            logger.warning("No data for model performance monitoring.")
            return {"status": "No data"}

        logger.info("[ANALYZE] Monitoring model performance...")

        # Merge predictions with actual outcomes (assuming a common 'trade_id' or similar)
        # For simplicity, let's assume model_predictions_df has 'trade_id', 'predicted_direction', 'predicted_roi', 'prediction_confidence'
        # and actual_trades_df has 'trade_id', 'actual_direction', 'roi_percent'
        merged_df = model_predictions_df.join(actual_trades_df, on="trade_id", how="inner")

        if merged_df.is_empty():
            logger.warning("No matching trades for model performance monitoring after merge.")
            return {"status": "No matching data"}

        # Accuracy of predicted direction
        accuracy_direction = (pl.col("predicted_direction") == pl.col("actual_direction")).mean().alias("accuracy_direction")
        
        # Predicted vs actual ROI (e.g., Mean Absolute Error or R-squared)
        predicted_vs_actual_roi_mae = (pl.col("predicted_roi") - pl.col("roi_percent")).abs().mean().alias("predicted_vs_actual_roi_mae")

        # Confidence calibration curves (conceptual: requires binning confidence and checking accuracy in each bin)
        # For now, just a simple average confidence and average accuracy.
        avg_confidence = pl.col("prediction_confidence").mean().alias("average_confidence")
        
        model_summary = merged_df.select([
            accuracy_direction,
            predicted_vs_actual_roi_mae,
            avg_confidence
        ]).row(0, named=True)

        # Drift detection (conceptual: compare current accuracy with historical baseline)
        # Assume a historical baseline is available, e.g., from config or a loaded file
        historical_accuracy_baseline = 0.67 # Example baseline
        drift_threshold = 0.10 # 10% drop

        current_accuracy = model_summary["accuracy_direction"]
        drift = historical_accuracy_baseline - current_accuracy
        drift_detected = drift > drift_threshold

        alerts = []
        if drift_detected:
            alerts.append(f"Model accuracy dropped from {historical_accuracy_baseline*100:.0f}% → {current_accuracy*100:.0f}% (Drift: {drift*100:.0f}%)")
            logger.warning(alerts[-1])

        # Recommends retraining if: Drift > threshold or Live Sharpe < backtest Sharpe or Live drawdown breach
        # Live Sharpe and Live drawdown breach would require more complex calculations over time.
        # For now, focus on drift.
        recommend_retraining = drift_detected # Simplified

        if recommend_retraining:
            alerts.append("Recommendation: Retrain model due to significant drift.")
            logger.info(alerts[-1])

        return {
            "accuracy_direction": round(current_accuracy, 4),
            "predicted_vs_actual_roi_mae": round(model_summary["predicted_vs_actual_roi_mae"], 4),
            "average_confidence": round(model_summary["average_confidence"], 4),
            "drift_detected": drift_detected,
            "drift_percentage": round(drift, 4),
            "recommend_retraining": recommend_retraining,
            "alerts": alerts
        }

    async def _evaluate_regime_performance(self, evaluated_trades_df: pl.DataFrame) -> Dict[str, Any]:
        """
        Groups trade outcomes by market regime, volatility regime, time of day, expiry proximity, news day.
        Finds which strategy excels in what regime.
        """
        if evaluated_trades_df.is_empty():
            logger.warning("No evaluated trades to analyze for regime performance.")
            return {}

        logger.info("[ANALYZE] Evaluating regime-based strategy performance...")

        # Ensure 'entry_time' is datetime and extract time of day
        evaluated_trades_df = evaluated_trades_df.with_columns([
            pl.col("entry_time").cast(pl.Datetime),
            pl.col("entry_time").dt.hour().alias("hour_of_day")
        ])

        # Define regime grouping columns
        regime_cols = ["strategy_id", "market_regime", "volatility_regime", "hour_of_day", "expiry_proximity", "news_day"]

        # Aggregate performance (e.g., average ROI, win rate) for each regime combination
        regime_performance = evaluated_trades_df.group_by(regime_cols).agg([
            pl.col("roi_percent").mean().alias("avg_roi"),
            (pl.col("pnl_category") == "Profit").mean().alias("win_rate"),
            pl.len().alias("trade_count")
        ]).sort(regime_cols)

        # Find best/worst performing strategies in each regime
        regime_insights = {}
        for regime_group, group_df in regime_performance.group_by(["market_regime", "volatility_regime", "hour_of_day", "expiry_proximity", "news_day"]):
            if group_df.is_empty():
                continue
            
            best_strategy = group_df.sort("avg_roi", descending=True).head(1)
            worst_strategy = group_df.sort("avg_roi", descending=False).head(1)

            regime_key = "_".join(map(str, regime_group))
            regime_insights[regime_key] = {
                "best_strategy": best_strategy.select(["strategy_id", "avg_roi", "win_rate", "trade_count"]).row(0, named=True) if not best_strategy.is_empty() else None,
                "worst_strategy": worst_strategy.select(["strategy_id", "avg_roi", "win_rate", "trade_count"]).row(0, named=True) if not worst_strategy.is_empty() else None,
            }
        
        logger.info("Regime-based strategy evaluation complete.")
        return regime_insights

    async def _provide_risk_control_feedback(self, evaluated_trades_df: pl.DataFrame) -> Dict[str, Any]:
        """
        Compares actual loss vs allowed capital at risk and daily drawdown vs threshold.
        Evaluates if trading was paused at the right time and if risky signals were filtered.
        Sends reports to Risk Management Agent (simulated).
        """
        if evaluated_trades_df.is_empty():
            logger.warning("No evaluated trades for risk control feedback.")
            return {}

        logger.info("[ANALYZE] Providing risk control feedback...")

        feedback = {}

        # Aggregate daily for drawdown analysis
        daily_performance = evaluated_trades_df.group_by(pl.col("entry_time").dt.date().alias("trade_date")).agg([
            pl.col("absolute_pnl").sum().alias("daily_pnl"),
            pl.col("actual_loss").sum().alias("total_actual_loss"),
            pl.col("allowed_capital_at_risk").sum().alias("total_allowed_capital_at_risk"),
            pl.col("daily_drawdown_threshold").first().alias("daily_dd_threshold"), # Assuming threshold is constant per day
            pl.col("trading_paused").any().alias("was_trading_paused"),
            pl.col("risky_signals_filtered").all().alias("all_risky_signals_filtered") # All signals were filtered
        ]).sort("trade_date")

        # Evaluate "Was trading paused at right time?"
        # This is complex and requires knowing the real-time drawdown path.
        # For simplicity, we check if daily_pnl is negative and trading was paused.
        trading_pause_evaluations = []
        for row in daily_performance.iter_rows(named=True):
            if row["daily_pnl"] < 0 and row["was_trading_paused"]:
                trading_pause_evaluations.append(f"On {row['trade_date']}, trading was paused during a loss-making day. ✅")
            elif row["daily_pnl"] < 0 and not row["was_trading_paused"]:
                trading_pause_evaluations.append(f"On {row['trade_date']}, trading was NOT paused during a loss-making day. ❌")
            elif row["daily_pnl"] >= 0 and row["was_trading_paused"]:
                trading_pause_evaluations.append(f"On {row['trade_date']}, trading was paused on a profitable day. Consider if pause was premature. ⚠️")
        feedback["trading_pause_evaluations"] = trading_pause_evaluations

        # Evaluate "Were risky signals filtered correctly?"
        risky_signal_evaluations = []
        if not daily_performance.is_empty():
            if daily_performance.select(pl.col("all_risky_signals_filtered")).min().item():
                risky_signal_evaluations.append("All risky signals appear to have been filtered correctly. ✅")
            else:
                risky_signal_evaluations.append("Some risky signals might not have been filtered. Review required. ❌")
        feedback["risky_signal_evaluations"] = risky_signal_evaluations

        # Compare actual loss vs allowed capital at risk
        capital_at_risk_breaches = evaluated_trades_df.filter(pl.col("actual_loss").abs() > pl.col("allowed_capital_at_risk")).select(["trade_id", "actual_loss", "allowed_capital_at_risk"])
        if not capital_at_risk_breaches.is_empty():
            feedback["capital_at_risk_breaches"] = capital_at_risk_breaches.to_dicts()
            logger.warning(f"Detected {len(capital_at_risk_breaches)} instances where actual loss exceeded allowed capital at risk. 🚨")
        else:
            feedback["capital_at_risk_breaches"] = "No breaches detected. ✅"

        # Compare daily drawdown vs threshold (conceptual, needs actual drawdown calculation)
        # For now, we'll just check if daily_pnl is below a hypothetical threshold.
        daily_drawdown_breaches = daily_performance.filter(pl.col("daily_pnl") < -pl.col("daily_dd_threshold")).select(["trade_date", "daily_pnl", "daily_dd_threshold"])
        if not daily_drawdown_breaches.is_empty():
            feedback["daily_drawdown_breaches"] = daily_drawdown_breaches.to_dicts()
            logger.warning(f"Detected {len(daily_drawdown_breaches)} instances where daily P&L breached drawdown threshold. 📉")
        else:
            feedback["daily_drawdown_breaches"] = "No daily drawdown breaches detected. ✅"

        # Simulate sending reports to Risk Management Agent
        logger.info("Simulating sending risk control feedback to Risk Management Agent. 📧")
        # In a real system, this would involve calling another agent's method or an API endpoint.
        
        return feedback

    async def _generate_trade_session_summary(self, evaluated_trades_df: pl.DataFrame, period: str = "daily") -> Dict[str, Any]:
        """
        Creates daily P&L summary, weekly insights, win/loss breakdown by strategy, time, signal source.
        Suggests strategy adjustments.
        """
        if evaluated_trades_df.is_empty():
            logger.warning("No evaluated trades to generate session summary.")
            return {}

        logger.info(f"[REPORT] Generating trade session summary for period: {period}...")

        summary = {}
        
        # Ensure 'entry_time' is datetime
        evaluated_trades_df = evaluated_trades_df.with_columns(
            pl.col("entry_time").cast(pl.Datetime)
        )

        # Add hour_of_day column if not present
        if "hour_of_day" not in evaluated_trades_df.columns:
            evaluated_trades_df = evaluated_trades_df.with_columns([
                pl.col("entry_time").dt.hour().alias("hour_of_day")
            ])

        # Group by date for daily summary
        daily_summary_df = evaluated_trades_df.group_by(pl.col("entry_time").dt.date().alias("trade_date")).agg([
            pl.len().alias("total_trades"),
            (pl.col("pnl_category") == "Profit").sum().alias("wins"),
            (pl.col("pnl_category") == "Loss").sum().alias("losses"),
            pl.col("roi_percent").sum().alias("total_roi_percent"),
            pl.col("absolute_pnl").sum().alias("net_pnl"),
            pl.col("strategy_id").first().alias("strategy_breakdown"),  # Simplified
            pl.col("signal_source").first().alias("signal_source_breakdown"),  # Simplified
            pl.col("hour_of_day").first().alias("time_of_day_breakdown"),  # Simplified
        ]).sort("trade_date")

        daily_summaries = []
        for row in daily_summary_df.iter_rows(named=True):
            daily_summaries.append({
                "date": str(row["trade_date"]),
                "total_trades": row["total_trades"],
                "wins": row["wins"],
                "losses": row["losses"],
                "total_roi": round(row["total_roi_percent"], 2),
                "net_pnl": round(row["net_pnl"], 2),
                "strategy_breakdown": str(row["strategy_breakdown"]),
                "signal_source_breakdown": str(row["signal_source_breakdown"]),
                "time_of_day_breakdown": str(row["time_of_day_breakdown"]),
            })
        summary["daily_summaries"] = daily_summaries

        # Weekly insights (conceptual: would aggregate daily summaries)
        # For now, a simple overall summary
        total_trades = evaluated_trades_df.height
        total_wins = (evaluated_trades_df.select(pl.col("pnl_category") == "Profit").sum()).item()
        total_roi = evaluated_trades_df.select(pl.col("roi_percent").sum()).item()
        net_pnl = evaluated_trades_df.select(pl.col("absolute_pnl").sum()).item()

        summary["overall_insights"] = {
            "total_trades": total_trades,
            "total_wins": total_wins,
            "total_losses": total_trades - total_wins,
            "total_roi": round(total_roi, 2),
            "net_pnl": round(net_pnl, 2),
            "win_rate": round((total_wins / total_trades * 100) if total_trades > 0 else 0, 2)
        }

        # Suggested strategy adjustments (conceptual: based on performance, e.g., from regime analysis)
        # This would typically come from the Strategy Evolution Feedback Loop.
        summary["suggested_strategy_adjustments"] = [
            "Review strategies under high volatility regimes.",
            "Consider reducing sizing for strategies with low win rates but high expectancy.",
            "Analyze entry precision for CE trades during market lows."
        ]

        # Example for LLM Interface Agent
        if not daily_summaries:
            summary["llm_interface_example"] = "No trades recorded for yesterday."
        else:
            yesterday_summary = daily_summaries[-1] # Get the most recent day
            llm_example = (
                f"Yesterday: {yesterday_summary['total_trades']} trades, "
                f"{yesterday_summary['wins']} wins, total ROI: +{yesterday_summary['total_roi']}%. "
                f"Net P&L: ₹{yesterday_summary['net_pnl']}. "
                f"Best performer: [Strategy from regime analysis] in a [Regime] with [IV spike]. " # Needs actual regime data
                f"Worst: [Strategy from regime analysis], [X] SL hits in volatile choppy session." # Needs actual regime data
            )
            summary["llm_interface_example"] = llm_example
        
        logger.info("Trade session summary generation complete. 📝")
        return summary

    async def _detect_anomalies(self, evaluated_trades_df: pl.DataFrame) -> Dict[str, Any]:
        """
        Flags anomalies like trade without signal, trade exited too early, unrealistic ROI spikes,
        execution failure causing P&L deviation.
        Alerts developers or triggers auto-disable of faulty components.
        """
        if evaluated_trades_df.is_empty():
            logger.warning("No evaluated trades to detect anomalies.")
            return {}

        logger.info("[ANALYZE] Detecting anomalies and errors... 🚨")

        anomalies = []

        # Trade without signal (conceptual: assuming 'signal_source' or 'signal_confidence' indicates a signal)
        trades_without_signal = evaluated_trades_df.filter(pl.col("signal_source").is_null() | (pl.col("signal_confidence").is_null()))
        if not trades_without_signal.is_empty():
            anomalies.append({
                "type": "Trade without signal",
                "count": trades_without_signal.height,
                "details": trades_without_signal.select(["trade_id", "entry_time", "strategy_id"]).to_dicts()
            })
            logger.warning(f"Detected {trades_without_signal.height} trades without a clear signal. ⚠️")

        # Trade exited too early (conceptual: e.g., holding time below a minimum threshold for profitable trades)
        min_holding_time_for_profit = 1 # minutes
        trades_exited_too_early = evaluated_trades_df.filter(
            (pl.col("pnl_category") == "Profit") & (pl.col("holding_time_minutes") < min_holding_time_for_profit)
        )
        if not trades_exited_too_early.is_empty():
            anomalies.append({
                "type": "Trade exited too early (profitable)",
                "count": trades_exited_too_early.height,
                "details": trades_exited_too_early.select(["trade_id", "holding_time_minutes", "roi_percent"]).to_dicts()
            })
            logger.warning(f"Detected {trades_exited_too_early.height} profitable trades exited too early. ⏱️")

        # Unrealistic ROI spikes (data issue) - e.g., ROI > 1000%
        unrealistic_roi_threshold = 1000 # percent
        roi_spikes = evaluated_trades_df.filter(pl.col("roi_percent").abs() > unrealistic_roi_threshold)
        if not roi_spikes.is_empty():
            anomalies.append({
                "type": "Unrealistic ROI spike (potential data issue)",
                "count": roi_spikes.height,
                "details": roi_spikes.select(["trade_id", "roi_percent", "entry_price", "exit_price"]).to_dicts()
            })
            logger.critical(f"Detected {roi_spikes.height} unrealistic ROI spikes. Data integrity check needed! 💥")

        # Execution failure causing P&L deviation (conceptual: large slippage leading to unexpected loss)
        # Assuming 'slippage_reason' can indicate this.
        execution_failures = evaluated_trades_df.filter(
            (pl.col("slippage_reason") == "Significant Slippage") & (pl.col("pnl_category") == "Loss")
        )
        if not execution_failures.is_empty():
            anomalies.append({
                "type": "Execution failure causing P&L deviation",
                "count": execution_failures.height,
                "details": execution_failures.select(["trade_id", "entry_slippage", "exit_slippage", "absolute_pnl"]).to_dicts()
            })
            logger.warning(f"Detected {execution_failures.height} execution failures leading to P&L deviation. ⚙️")

        # Alerts developers or triggers auto-disable of faulty components (simulated)
        if anomalies:
            logger.critical("Anomaly detection triggered. Alerting developers and considering auto-disable of faulty components. 🛑")
            # In a real system, this would trigger an alert system (e.g., email, Slack, PagerDuty)
            # and potentially interact with an "Agent Orchestrator" to disable components.

        return {"anomalies_detected": bool(anomalies), "details": anomalies}

    async def _store_performance_data(self, data: Dict[str, Any], data_type: str):
        """
        Stores performance data (e.g., PnL curves, Sharpe timeline) to Parquet files.
        """
        output_dir = Path("data/performance")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_path = output_dir / f"{data_type}_performance_{timestamp}.parquet"

        logger.info(f"[STORAGE] Storing {data_type} performance data to {file_path}...")
        
        # Convert dictionary to Polars DataFrame for storage
        # This part needs to be flexible based on the 'data' structure.
        # For simplicity, assuming 'data' is a list of dicts or a dict that can be converted.
        try:
            if isinstance(data, dict):
                # If it's a dict of dicts (like strategy_metrics), flatten it
                records = []
                for strat_id, periods in data.items():
                    for period_key, metrics in periods.items():
                        record = {"strategy_id": strat_id, "period_key": period_key}
                        record.update(metrics)
                        records.append(record)
                if records:
                    df_to_save = pl.DataFrame(records)
                else:
                    logger.warning(f"No records to save for {data_type}.")
                    return
            elif isinstance(data, list) and all(isinstance(i, dict) for i in data):
                df_to_save = pl.DataFrame(data)
            else:
                logger.error(f"Unsupported data format for storage: {type(data)}")
                return

            df_to_save.write_parquet(file_path)
            logger.info(f"[SUCCESS] Stored {data_type} performance data to {file_path}. 💾")
        except Exception as e:
            logger.error(f"[ERROR] Failed to store {data_type} performance data: {e}")

    async def _generate_ai_labels(self, evaluated_trades_df: pl.DataFrame) -> pl.DataFrame:
        """
        Provides labels for AI Training Agent:
        is_profitable, was_model_correct, expected_vs_actual_roi_gap, signal_quality_tag, suitable_for_training
        """
        if evaluated_trades_df.is_empty():
            logger.warning("No evaluated trades to generate AI labels.")
            return evaluated_trades_df

        logger.info("[LABELING] Generating AI training labels... 🏷️")

        # First add is_profitable column
        labeled_df = evaluated_trades_df.with_columns([
            (pl.col("absolute_pnl") > 0).alias("is_profitable")
        ])

        # Then add other columns that may depend on is_profitable
        labeled_df = labeled_df.with_columns([
            # was_model_correct (conceptual: if model predicted direction matches actual outcome)
            # Requires 'model_predicted_direction' and 'actual_direction'
            (pl.col("model_predicted_direction") == pl.col("actual_direction")).alias("was_model_correct"),
            # expected_vs_actual_roi_gap
            (pl.col("model_predicted_roi") - pl.col("roi_percent")).abs().alias("expected_vs_actual_roi_gap"),
            # signal_quality_tag (high, mid, low based on confidence and outcome)
            pl.when((pl.col("signal_confidence") > 0.7) & pl.col("is_profitable"))
            .then(pl.lit("high"))
            .when((pl.col("signal_confidence") > 0.5) & pl.col("is_profitable"))
            .then(pl.lit("mid"))
            .otherwise(pl.lit("low")).alias("signal_quality_tag"),
            # suitable_for_training (e.g., exclude trades with anomalies or very low confidence)
            pl.lit(True).alias("suitable_for_training") # Default to True, can be set to False based on anomaly detection
        ])
        
        # Example of setting suitable_for_training based on anomalies (conceptual)
        # If a trade was flagged as having an "Unrealistic ROI spike", it might not be suitable for training.
        # This would require joining with anomaly detection results or passing anomaly flags.
        # For now, it's always True.

        logger.info("AI training labels generated.")
        return labeled_df

    async def _provide_strategy_evolution_feedback(self, strategy_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """
        Recommends parameter tuning, strategy deprecation, fusion of multiple strategies.
        Logs meta information.
        """
        if not strategy_metrics:
            logger.warning("No strategy metrics available for evolution feedback.")
            return {}

        logger.info("[FEEDBACK] Providing strategy evolution feedback... 🔄")

        feedback = {
            "recommendations": [],
            "meta_logs": []
        }

        # Iterate through strategies and their performance
        for strategy_id, periods_data in strategy_metrics.items():
            # Assuming we look at the most recent period for feedback
            # This needs to be refined to look at trends over time.
            if not periods_data:
                continue
            
            # Get the latest period's data (assuming keys are sortable dates/periods)
            latest_period_key = sorted(periods_data.keys())[-1]
            latest_metrics = periods_data[latest_period_key]

            win_rate = latest_metrics.get("win_rate", 0)
            avg_roi = latest_metrics.get("avg_roi", 0)
            expectancy = latest_metrics.get("expectancy", 0)
            sharpe = latest_metrics.get("sharpe", 0)
            sample_size = latest_metrics.get("sample_size", 0)

            # Parameter tuning recommendations
            if sample_size > 30: # Only if enough data
                if sharpe < 0.5 and avg_roi > 0:
                    feedback["recommendations"].append(f"Strategy {strategy_id}: Low Sharpe Ratio ({sharpe:.2f}) despite positive ROI. Consider parameter tuning for risk reduction. 🛠️")
                if win_rate < 40 and expectancy > 0.1:
                    feedback["recommendations"].append(f"Strategy {strategy_id}: Low win rate ({win_rate:.1f}%) but high expectancy ({expectancy:.2f}). Focus on reducing trade frequency or increasing average win size. 📉➡️📈")
                    feedback["meta_logs"].append(f"Strat_{strategy_id} has low win rate but high expectancy → reduce sizing, not disable.")

            # Strategy deprecation
            if sample_size > 50 and sharpe < 0 and avg_roi < -5:
                feedback["recommendations"].append(f"Strategy {strategy_id}: Consistently negative Sharpe ({sharpe:.2f}) and ROI ({avg_roi:.2f}%). Consider deprecating this strategy. 🗑️")
                feedback["meta_logs"].append(f"Strat_{strategy_id} shows consistent underperformance. Review for deprecation.")

            # Fusion of multiple strategies (conceptual: if two strategies perform well in complementary regimes)
            # This would require more advanced analysis of regime performance.
            # Placeholder:
            if strategy_id == "strat_004" and latest_period_key.startswith("2025-05"): # Example condition
                 feedback["meta_logs"].append(f"Strat_004 peaked in May but deteriorated post-event X (hypothetical event).")

        logger.info("Strategy evolution feedback generated. 💡")
        return feedback

    async def _generate_dashboard_output(self, overall_summary: Dict[str, Any], alerts: List[str]) -> Dict[str, Any]:
        """
        Generates the performance dashboard output in JSON format.
        """
        logger.info("[OUTPUT] Generating performance dashboard output... 📊")

        # Use overall_summary from _generate_trade_session_summary
        total_trades = overall_summary.get("total_trades", 0)
        net_pnl = overall_summary.get("net_pnl", 0)
        roi = overall_summary.get("total_roi", 0) # This is sum of ROI, not average. Needs adjustment if average is desired.
        
        # Sharpe and Drawdown would ideally come from a comprehensive portfolio-level calculation
        # For now, using placeholders or deriving from aggregated strategy metrics if available.
        # Let's assume we have a way to get overall Sharpe and Drawdown.
        # For this example, I'll use dummy values or try to infer from existing data.
        
        # To get best/worst strategy, we need to look at aggregated strategy metrics.
        # This function would typically receive the output of _aggregate_strategy_metrics.
        # For now, I'll use dummy values.
        best_strategy = "strat_009" # Placeholder
        worst_strategy = "strat_021" # Placeholder
        sharpe = 1.51 # Placeholder
        drawdown = 2.1 # Placeholder

        dashboard_output = {
            "date": datetime.now().strftime("%Y-%m-%d"),
            "total_trades": total_trades,
            "net_pnl": round(net_pnl, 2),
            "roi": round(roi, 2),
            "sharpe": round(sharpe, 2),
            "best_strategy": best_strategy,
            "worst_strategy": worst_strategy,
            "drawdown": round(drawdown, 2),
            "alerts": alerts
        }
        
        logger.info("Performance dashboard output generated. ✅")
        return dashboard_output

    async def _generate_heatmaps(self, evaluated_trades_df: pl.DataFrame) -> Dict[str, Any]:
        """
        🔥 NEW FEATURE: Generate performance heatmaps
        """
        if evaluated_trades_df.is_empty():
            return {}

        logger.info("🔥 Generating performance heatmaps...")

        heatmaps = {}

        # Ensure hour_of_day column exists
        if "hour_of_day" not in evaluated_trades_df.columns:
            evaluated_trades_df = evaluated_trades_df.with_columns([
                pl.col("entry_time").dt.hour().alias("hour_of_day")
            ])

        # Hour-of-day success heatmap
        hourly_performance = evaluated_trades_df.group_by("hour_of_day").agg([
            pl.col("roi_percent").mean().alias("avg_roi"),
            (pl.col("pnl_category") == "Profit").mean().alias("win_rate"),
            pl.len().alias("trade_count")
        ]).sort("hour_of_day")

        heatmaps["hourly_performance"] = hourly_performance.to_dicts()

        # Strategy vs Market Regime heatmap
        strategy_regime_performance = evaluated_trades_df.group_by(["strategy_id", "market_regime"]).agg([
            pl.col("roi_percent").mean().alias("avg_roi"),
            (pl.col("pnl_category") == "Profit").mean().alias("win_rate"),
            pl.len().alias("trade_count")
        ]).sort(["strategy_id", "market_regime"])

        heatmaps["strategy_regime_performance"] = strategy_regime_performance.to_dicts()

        # Strike-wise profitability (if strike data available)
        if "strike_price" in evaluated_trades_df.columns:
            # Create strike buckets for better visualization
            strike_performance = evaluated_trades_df.with_columns([
                pl.when(pl.col("strike_price") < pl.col("underlying_entry_price") * 0.95)
                .then(pl.lit("Deep OTM"))
                .when(pl.col("strike_price") < pl.col("underlying_entry_price") * 0.98)
                .then(pl.lit("OTM"))
                .when(pl.col("strike_price") < pl.col("underlying_entry_price") * 1.02)
                .then(pl.lit("ATM"))
                .when(pl.col("strike_price") < pl.col("underlying_entry_price") * 1.05)
                .then(pl.lit("ITM"))
                .otherwise(pl.lit("Deep ITM")).alias("moneyness")
            ]).group_by(["option_type", "moneyness"]).agg([
                pl.col("roi_percent").mean().alias("avg_roi"),
                (pl.col("pnl_category") == "Profit").mean().alias("win_rate"),
                pl.len().alias("trade_count")
            ])

            heatmaps["strike_profitability"] = strike_performance.to_dicts()

        # Expiry-day behavior heatmap
        if "expiry_proximity" in evaluated_trades_df.columns:
            expiry_performance = evaluated_trades_df.group_by("expiry_proximity").agg([
                pl.col("roi_percent").mean().alias("avg_roi"),
                (pl.col("pnl_category") == "Profit").mean().alias("win_rate"),
                pl.col("theta_pnl").mean().alias("avg_theta_pnl"),
                pl.len().alias("trade_count")
            ]).sort("expiry_proximity")

            heatmaps["expiry_behavior"] = expiry_performance.to_dicts()

        logger.info("✅ Performance heatmaps generated.")
        return heatmaps

    async def _generate_pnl_curves(self, evaluated_trades_df: pl.DataFrame) -> Dict[str, Any]:
        """
        🔥 NEW FEATURE: Generate P&L curves for visualization
        """
        if evaluated_trades_df.is_empty():
            return {}

        logger.info("📈 Generating P&L curves...")

        pnl_curves = {}

        # Overall cumulative P&L curve (simplified)
        overall_pnl = evaluated_trades_df.sort("entry_time").select(["entry_time", "absolute_pnl"])
        # Use a simple running sum approach
        pnl_values = overall_pnl.select("absolute_pnl").to_numpy().flatten()
        cumulative_pnl = np.cumsum(pnl_values)

        overall_pnl = overall_pnl.with_columns([
            pl.Series("cumulative_pnl", cumulative_pnl)
        ])

        pnl_curves["overall_cumulative_pnl"] = overall_pnl.to_dicts()

        # Strategy-wise P&L curves (simplified)
        strategy_pnl = evaluated_trades_df.sort("entry_time").select(["entry_time", "strategy_id", "absolute_pnl"])
        pnl_curves["strategy_pnl_curves"] = strategy_pnl.to_dicts()

        # Greeks attribution curves (simplified)
        if "delta_pnl" in evaluated_trades_df.columns:
            greeks_curves = evaluated_trades_df.sort("entry_time").select([
                "entry_time", "delta_pnl", "gamma_pnl", "theta_pnl", "vega_pnl", "rho_pnl"
            ])
            pnl_curves["greeks_attribution_curves"] = greeks_curves.to_dicts()

        # Drawdown curve (simplified)
        drawdown_data = overall_pnl.with_columns([
            pl.lit(0.0).alias("drawdown_percent")  # Placeholder
        ]).select(["entry_time", "drawdown_percent"])

        pnl_curves["drawdown_curve"] = drawdown_data.to_dicts()

        logger.info("✅ P&L curves generated.")
        return pnl_curves

    async def _generate_enhanced_dashboard(self, evaluated_trades_df: pl.DataFrame, strategy_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """
        🔥 NEW FEATURE: Enhanced performance dashboard with comprehensive analytics
        """
        logger.info("🚀 Generating enhanced performance dashboard...")

        dashboard = {
            "timestamp": datetime.now().isoformat(),
            "summary": {},
            "performance_metrics": {},
            "risk_metrics": {},
            "heatmaps": {},
            "pnl_curves": {},
            "alerts": [],
            "insights": []
        }

        if not evaluated_trades_df.is_empty():
            # Basic summary
            total_trades = evaluated_trades_df.height
            total_pnl = evaluated_trades_df.select(pl.col("absolute_pnl").sum()).item()
            win_rate = (evaluated_trades_df.select(pl.col("pnl_category") == "Profit").sum()).item() / total_trades * 100

            dashboard["summary"] = {
                "total_trades": total_trades,
                "total_pnl": round(total_pnl, 2),
                "win_rate": round(win_rate, 2),
                "avg_trade_pnl": round(total_pnl / total_trades, 2) if total_trades > 0 else 0,
                "best_trade": evaluated_trades_df.select(pl.col("absolute_pnl").max()).item(),
                "worst_trade": evaluated_trades_df.select(pl.col("absolute_pnl").min()).item()
            }

            # Advanced performance metrics
            returns_series = evaluated_trades_df.select(pl.col("roi_percent") / 100).to_series()
            perf_metrics = await self._calculate_advanced_risk_metrics(returns_series)

            dashboard["performance_metrics"] = {
                "sharpe_ratio": round(perf_metrics.sharpe_ratio, 3),
                "sortino_ratio": round(perf_metrics.sortino_ratio, 3),
                "calmar_ratio": round(perf_metrics.calmar_ratio, 3),
                "profit_factor": round(perf_metrics.profit_factor, 3)
            }

            dashboard["risk_metrics"] = {
                "max_drawdown": round(perf_metrics.max_drawdown * 100, 2),
                "var_95": round(perf_metrics.var_95 * 100, 2),
                "cvar_95": round(perf_metrics.cvar_95 * 100, 2)
            }

            # Generate visualizations
            dashboard["heatmaps"] = await self._generate_heatmaps(evaluated_trades_df)
            dashboard["pnl_curves"] = await self._generate_pnl_curves(evaluated_trades_df)

            # Monte Carlo analysis
            mc_results = await self._monte_carlo_simulation(returns_series)
            dashboard["monte_carlo"] = mc_results

            # Stress testing
            stress_results = await self._stress_testing(evaluated_trades_df)
            dashboard["stress_testing"] = stress_results

            # Generate alerts based on thresholds
            alert_thresholds = self.config['notification_settings']['alert_thresholds']

            if abs(perf_metrics.max_drawdown) > alert_thresholds['max_drawdown']:
                alert_msg = f"🚨 Maximum drawdown exceeded threshold: {abs(perf_metrics.max_drawdown)*100:.2f}%"
                dashboard["alerts"].append(alert_msg)
                await self._send_notification("High Drawdown Alert", alert_msg)

            if perf_metrics.var_95 < -alert_thresholds['daily_loss']:
                alert_msg = f"⚠️ Daily VaR indicates high risk: {perf_metrics.var_95*100:.2f}%"
                dashboard["alerts"].append(alert_msg)
                await self._send_notification("High Risk Alert", alert_msg)

            # Generate insights
            dashboard["insights"] = await self._generate_ai_insights(evaluated_trades_df, strategy_metrics)

        logger.info("✅ Enhanced dashboard generated.")
        return dashboard

    async def _send_notification(self, title: str, message: str):
        """Send Windows notification if enabled"""
        if (self.config['notification_settings']['enable_windows_notifications'] and
            WINDOWS_NOTIFICATIONS and self.toaster):
            try:
                self.toaster.show_toast(title, message, duration=10, icon_path=None)
                logger.info(f"📢 Notification sent: {title}")
            except Exception as e:
                logger.warning(f"Failed to send notification: {e}")

    async def _generate_ai_insights(self, evaluated_trades_df: pl.DataFrame, strategy_metrics: Dict[str, Any]) -> List[str]:
        """
        🔥 NEW FEATURE: AI-powered insight generator
        """
        insights = []

        if evaluated_trades_df.is_empty():
            return ["No trades available for analysis."]

        logger.info("🧠 Generating AI-powered insights...")

        # Performance insights
        total_pnl = evaluated_trades_df.select(pl.col("absolute_pnl").sum()).item()
        win_rate = (evaluated_trades_df.select(pl.col("pnl_category") == "Profit").sum()).item() / evaluated_trades_df.height * 100

        if win_rate > 70:
            insights.append(f"🎯 Excellent win rate of {win_rate:.1f}%! Your strategy selection is performing well.")
        elif win_rate < 40:
            insights.append(f"⚠️ Low win rate of {win_rate:.1f}%. Consider reviewing entry criteria and risk management.")

        # Greeks analysis insights
        if "delta_pnl" in evaluated_trades_df.columns:
            delta_contribution = evaluated_trades_df.select(pl.col("delta_pnl").sum()).item()
            theta_contribution = evaluated_trades_df.select(pl.col("theta_pnl").sum()).item()
            vega_contribution = evaluated_trades_df.select(pl.col("vega_pnl").sum()).item()

            total_greeks_pnl = delta_contribution + theta_contribution + vega_contribution

            if abs(delta_contribution) > abs(total_greeks_pnl) * 0.6:
                insights.append(f"📊 Delta is your primary P&L driver (₹{delta_contribution:.0f}). Consider directional strategies.")

            if theta_contribution > 0:
                insights.append(f"⏰ Time decay is working in your favor (₹{theta_contribution:.0f}). Good theta management!")
            elif theta_contribution < -total_pnl * 0.2:
                insights.append(f"⚠️ Time decay is hurting performance (₹{theta_contribution:.0f}). Consider shorter-term trades.")

            if abs(vega_contribution) > abs(total_pnl) * 0.3:
                insights.append(f"🌊 Volatility changes significantly impact your P&L (₹{vega_contribution:.0f}). Monitor IV levels closely.")

        # Time-based insights
        if "hour_of_day" in evaluated_trades_df.columns:
            hourly_performance = evaluated_trades_df.group_by("hour_of_day").agg([
                pl.col("absolute_pnl").mean().alias("avg_pnl")
            ]).sort("avg_pnl", descending=True)

            best_hour = hourly_performance.head(1).select("hour_of_day").item()
            worst_hour = hourly_performance.tail(1).select("hour_of_day").item()

            insights.append(f"🕐 Best trading hour: {best_hour}:00. Worst: {worst_hour}:00. Consider timing optimization.")

        # Strategy insights
        if strategy_metrics:
            best_strategy = None
            worst_strategy = None
            best_roi = float('-inf')
            worst_roi = float('inf')

            for strategy_id, periods in strategy_metrics.items():
                for period, metrics in periods.items():
                    roi = metrics.get('avg_roi', 0)
                    if roi > best_roi:
                        best_roi = roi
                        best_strategy = strategy_id
                    if roi < worst_roi:
                        worst_roi = roi
                        worst_strategy = strategy_id

            if best_strategy:
                insights.append(f"🏆 Top performer: {best_strategy} with {best_roi:.2f}% avg ROI.")
            if worst_strategy and worst_roi < 0:
                insights.append(f"📉 Underperformer: {worst_strategy} with {worst_roi:.2f}% avg ROI. Consider review.")

        # Market regime insights
        if "market_regime" in evaluated_trades_df.columns:
            regime_performance = evaluated_trades_df.group_by("market_regime").agg([
                pl.col("absolute_pnl").mean().alias("avg_pnl"),
                (pl.col("pnl_category") == "Profit").mean().alias("win_rate")
            ]).sort("avg_pnl", descending=True)

            best_regime = regime_performance.head(1)
            if not best_regime.is_empty():
                regime_name = best_regime.select("market_regime").item()
                regime_pnl = best_regime.select("avg_pnl").item()
                insights.append(f"🌟 Best market regime: {regime_name} with ₹{regime_pnl:.0f} avg P&L per trade.")

        logger.info("✅ AI insights generated.")
        return insights

    async def _export_to_excel(self, data: Dict[str, Any], filename: str) -> str:
        """
        🔥 NEW FEATURE: Export performance data to Excel
        """
        logger.info(f"📊 Exporting data to Excel: {filename}")

        export_file = self.export_path / f"{filename}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        try:
            with pd.ExcelWriter(export_file, engine='openpyxl') as writer:
                # Summary sheet
                if 'summary' in data:
                    summary_df = pd.DataFrame([data['summary']])
                    summary_df.to_excel(writer, sheet_name='Summary', index=False)

                # Performance metrics
                if 'performance_metrics' in data:
                    perf_df = pd.DataFrame([data['performance_metrics']])
                    perf_df.to_excel(writer, sheet_name='Performance', index=False)

                # Risk metrics
                if 'risk_metrics' in data:
                    risk_df = pd.DataFrame([data['risk_metrics']])
                    risk_df.to_excel(writer, sheet_name='Risk', index=False)

                # Heatmaps
                if 'heatmaps' in data:
                    for heatmap_name, heatmap_data in data['heatmaps'].items():
                        if heatmap_data:
                            heatmap_df = pd.DataFrame(heatmap_data)
                            sheet_name = heatmap_name[:31]  # Excel sheet name limit
                            heatmap_df.to_excel(writer, sheet_name=sheet_name, index=False)

                # P&L curves
                if 'pnl_curves' in data:
                    for curve_name, curve_data in data['pnl_curves'].items():
                        if curve_data:
                            curve_df = pd.DataFrame(curve_data)
                            sheet_name = curve_name[:31]
                            curve_df.to_excel(writer, sheet_name=sheet_name, index=False)

            logger.info(f"✅ Excel export completed: {export_file}")
            return str(export_file)

        except Exception as e:
            logger.error(f"❌ Excel export failed: {e}")
            return ""

    async def _export_to_json(self, data: Dict[str, Any], filename: str) -> str:
        """Export performance data to JSON"""
        logger.info(f"📄 Exporting data to JSON: {filename}")

        export_file = self.export_path / f"{filename}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        try:
            with open(export_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, default=str)

            logger.info(f"✅ JSON export completed: {export_file}")
            return str(export_file)

        except Exception as e:
            logger.error(f"❌ JSON export failed: {e}")
            return ""

    async def _natural_language_query(self, query: str, evaluated_trades_df: pl.DataFrame) -> str:
        """
        🔥 NEW FEATURE: Natural language query interface
        """
        logger.info(f"🗣️ Processing natural language query: {query}")

        query_lower = query.lower()

        if evaluated_trades_df.is_empty():
            return "No trade data available to answer your query."

        try:
            # Performance queries
            if "win rate" in query_lower or "success rate" in query_lower:
                win_rate = (evaluated_trades_df.select(pl.col("pnl_category") == "Profit").sum()).item() / evaluated_trades_df.height * 100
                return f"Your current win rate is {win_rate:.2f}% based on {evaluated_trades_df.height} trades."

            elif "total pnl" in query_lower or "total profit" in query_lower:
                total_pnl = evaluated_trades_df.select(pl.col("absolute_pnl").sum()).item()
                return f"Your total P&L is ₹{total_pnl:,.2f}."

            elif "best strategy" in query_lower:
                strategy_performance = evaluated_trades_df.group_by("strategy_id").agg([
                    pl.col("absolute_pnl").mean().alias("avg_pnl")
                ]).sort("avg_pnl", descending=True).head(1)

                if not strategy_performance.is_empty():
                    best_strategy = strategy_performance.select("strategy_id").item()
                    best_pnl = strategy_performance.select("avg_pnl").item()
                    return f"Your best performing strategy is {best_strategy} with an average P&L of ₹{best_pnl:.2f} per trade."

            elif "worst trade" in query_lower or "biggest loss" in query_lower:
                worst_trade = evaluated_trades_df.sort("absolute_pnl").head(1)
                if not worst_trade.is_empty():
                    loss_amount = worst_trade.select("absolute_pnl").item()
                    trade_id = worst_trade.select("trade_id").item()
                    return f"Your worst trade was {trade_id} with a loss of ₹{abs(loss_amount):,.2f}."

            elif "best trade" in query_lower or "biggest profit" in query_lower:
                best_trade = evaluated_trades_df.sort("absolute_pnl", descending=True).head(1)
                if not best_trade.is_empty():
                    profit_amount = best_trade.select("absolute_pnl").item()
                    trade_id = best_trade.select("trade_id").item()
                    return f"Your best trade was {trade_id} with a profit of ₹{profit_amount:,.2f}."

            elif "drawdown" in query_lower:
                # Calculate drawdown using numpy for simplicity
                pnl_values = evaluated_trades_df.sort("entry_time").select("absolute_pnl").to_numpy().flatten()
                cumulative_pnl = np.cumsum(pnl_values)
                running_max = np.maximum.accumulate(cumulative_pnl)
                drawdown = np.min((cumulative_pnl - running_max) / running_max * 100)
                return f"Your maximum drawdown is {abs(drawdown):.2f}%."

            elif "greeks" in query_lower and "delta" in query_lower:
                if "delta_pnl" in evaluated_trades_df.columns:
                    delta_pnl = evaluated_trades_df.select(pl.col("delta_pnl").sum()).item()
                    return f"Delta contributed ₹{delta_pnl:,.2f} to your total P&L."

            elif "theta" in query_lower:
                if "theta_pnl" in evaluated_trades_df.columns:
                    theta_pnl = evaluated_trades_df.select(pl.col("theta_pnl").sum()).item()
                    if theta_pnl > 0:
                        return f"Time decay worked in your favor, contributing ₹{theta_pnl:,.2f} to your P&L."
                    else:
                        return f"Time decay cost you ₹{abs(theta_pnl):,.2f} in P&L."

            elif "volatility" in query_lower or "vega" in query_lower:
                if "vega_pnl" in evaluated_trades_df.columns:
                    vega_pnl = evaluated_trades_df.select(pl.col("vega_pnl").sum()).item()
                    return f"Volatility changes contributed ₹{vega_pnl:,.2f} to your P&L."

            else:
                return "I can help you with queries about win rate, total P&L, best/worst strategies, trades, drawdown, and Greeks analysis. Please try rephrasing your question."

        except Exception as e:
            logger.error(f"Error processing query: {e}")
            return "Sorry, I encountered an error processing your query. Please try again."

    async def start(self, **kwargs) -> bool:
        """Start the performance analysis agent"""
        try:
            logger.info("[START] Starting Options Performance Analysis Agent...")
            self.is_running = True
            
            # Load trade data
            trades_df = await self._load_trade_data()
            if trades_df is None or trades_df.is_empty():
                logger.warning("No trade data available to perform analysis. Agent will run but with limited functionality.")
                # Still keep the agent running for future data, but skip analysis steps
                # await asyncio.sleep(self.config['analysis_interval'])
                # return True # Or False if no data means failure to start meaningfully

            # Start analysis tasks
            # These will be called once for demonstration, in a real system they might be periodic.
            if trades_df is not None and not trades_df.is_empty():
                evaluated_trades = await self._evaluate_trade_level_performance(trades_df)
                
                strategy_metrics_daily = await self._aggregate_strategy_metrics(evaluated_trades, period="daily")
                strategy_metrics_weekly = await self._aggregate_strategy_metrics(evaluated_trades, period="weekly")
                
                # Placeholder for model predictions data
                # model_predictions_df = pl.DataFrame({"trade_id": ["trade_1", "trade_2"], "predicted_direction": ["long", "short"], "predicted_roi": [0.05, -0.02], "prediction_confidence": [0.8, 0.6]})
                # model_performance = await self._monitor_model_performance(model_predictions_df, evaluated_trades)
                
                regime_performance_insights = await self._evaluate_regime_performance(evaluated_trades)
                risk_feedback = await self._provide_risk_control_feedback(evaluated_trades)
                session_summary = await self._generate_trade_session_summary(evaluated_trades)
                anomalies = await self._detect_anomalies(evaluated_trades)
                ai_labeled_trades = await self._generate_ai_labels(evaluated_trades)
                strategy_evolution_feedback = await self._provide_strategy_evolution_feedback(strategy_metrics_daily) # Using daily metrics for feedback
                
                # Store some of the generated data
                await self._store_performance_data(strategy_metrics_daily, "strategy_metrics_daily")
                await self._store_performance_data(regime_performance_insights, "regime_performance_insights")
                await self._store_performance_data(anomalies["details"], "anomalies_detected")
                await self._store_performance_data(ai_labeled_trades.to_dicts(), "ai_labeled_trades")

                # Generate dashboard output
                all_alerts = []
                # if model_performance and "alerts" in model_performance:
                #     all_alerts.extend(model_performance["alerts"])
                if anomalies and "details" in anomalies:
                    for anomaly in anomalies["details"]:
                        all_alerts.append(f"Anomaly: {anomaly['type']} ({anomaly['count']} instances)")

                # 🔥 NEW: Enhanced dashboard with comprehensive analytics
                enhanced_dashboard = await self._generate_enhanced_dashboard(evaluated_trades, strategy_metrics_daily)

                # Store additional performance data
                await self._store_performance_data(strategy_metrics_weekly, "strategy_metrics_weekly")
                await self._store_performance_data(enhanced_dashboard, "enhanced_dashboard")

                # 🔥 NEW: Export capabilities
                excel_file = await self._export_to_excel(enhanced_dashboard, "performance_dashboard")
                json_file = await self._export_to_json(enhanced_dashboard, "performance_dashboard")

                if excel_file:
                    logger.info(f"📊 Excel report exported: {excel_file}")
                if json_file:
                    logger.info(f"📄 JSON report exported: {json_file}")

                # Log enhanced dashboard summary
                logger.info("📊 Enhanced Dashboard Summary:")
                logger.info(f"   Total Trades: {enhanced_dashboard['summary'].get('total_trades', 0)}")
                logger.info(f"   Total P&L: ₹{enhanced_dashboard['summary'].get('total_pnl', 0):,.2f}")
                logger.info(f"   Win Rate: {enhanced_dashboard['summary'].get('win_rate', 0):.2f}%")
                logger.info(f"   Sharpe Ratio: {enhanced_dashboard['performance_metrics'].get('sharpe_ratio', 0):.3f}")
                logger.info(f"   Max Drawdown: {enhanced_dashboard['risk_metrics'].get('max_drawdown', 0):.2f}%")

                # Display alerts
                if enhanced_dashboard['alerts']:
                    logger.warning("🚨 Active Alerts:")
                    for alert in enhanced_dashboard['alerts']:
                        logger.warning(f"   {alert}")

                # Display insights
                if enhanced_dashboard['insights']:
                    logger.info("💡 AI Insights:")
                    for insight in enhanced_dashboard['insights']:
                        logger.info(f"   {insight}")

                # 🔥 NEW: Natural language query examples
                sample_queries = [
                    "What is my win rate?",
                    "What is my total PnL?",
                    "Which is my best strategy?",
                    "How much did theta contribute to my PnL?"
                ]

                logger.info("🗣️ Natural Language Query Examples:")
                for query in sample_queries[:2]:  # Show first 2 examples
                    response = await self._natural_language_query(query, evaluated_trades)
                    logger.info(f"   Q: {query}")
                    logger.info(f"   A: {response}")

                logger.info("✅ Enhanced Options Performance Analysis Agent completed comprehensive analysis cycle.")

            return True
        except Exception as e:
            logger.error(f"[ERROR] Failed to start agent: {e}")
            return False
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            logger.info("[CLEANUP] Cleaning up Options Performance Analysis Agent... 🧹")
            self.is_running = False
            logger.info("[SUCCESS] Options Performance Analysis Agent cleaned up")
        except Exception as e:
            logger.error(f"[ERROR] Cleanup failed: {e}")

# Example usage
async def main():
    agent = OptionsPerformanceAnalysisAgent()
    try:
        await agent.initialize()
        await agent.start()
    except KeyboardInterrupt:
        logger.info("Agent interrupted by user. 🛑")
    finally:
        await agent.cleanup()

if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    asyncio.run(main())
