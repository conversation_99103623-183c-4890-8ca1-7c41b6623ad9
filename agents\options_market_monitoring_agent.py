#!/usr/bin/env python3
"""
Options Market Monitoring Agent - Real-time Multi-Timeframe Market Surveillance

Features:
📊 1. Multi-Timeframe Option Chain Monitoring
- Real-time option chain tracking across 1min, 3min, 5min, 15min
- Strike price and expiry monitoring
- Volume and open interest analysis
- Bid-ask spread monitoring

📈 2. Greeks Monitoring & Analysis
- Real-time Greeks calculation
- Portfolio Greeks aggregation
- Greeks-based alerts and risk exposure
- Multi-timeframe Greeks trends

⚡ 3. Market Regime Detection
- Volatility regime identification across timeframes
- Trend detection for NIFTY & BANK NIFTY
- Market sentiment analysis
- Options flow analysis

🎯 4. Advanced Alert System
- Multi-timeframe price movement alerts
- Volatility spike detection
- Unusual options activity alerts
- Risk threshold breach notifications
"""

import asyncio
import logging
import polars as pl
from datetime import datetime, time, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any
import json
import yaml
import aiofiles
import polars_talib as pt

# Technical indicators integration (Polars-based)
from agents.polars_technical_indicators_manager import PolarsTechnicalIndicatorsManager, MarketRegime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class OptionsMarketMonitoringAgent:
    """Options Market Monitoring Agent for real-time market surveillance"""
    
    def __init__(self, config_path: str = "config/options_market_monitoring_config.yaml"):
        self.config_path = Path(config_path)
        self.config = None
        self.is_running = False

        # Data paths for multi-timeframe monitoring
        self.data_path = Path("data")
        self.live_path = self.data_path / "live"
        self.timeframes = ["1min", "3min", "5min", "15min"]

        # Market data cache for each timeframe
        self.market_data_cache = {tf: {} for tf in self.timeframes}
        self.last_update = {tf: {} for tf in self.timeframes}
        self.market_regime_state = {tf: {} for tf in self.timeframes}
        self.alerts_cache = []
        self.anomaly_logs = []
        self.strategy_suppression_decisions = []

        # Technical indicators manager for real market regime detection (Polars-based)
        self.indicators_manager = PolarsTechnicalIndicatorsManager()

        logger.info("[INIT] Options Market Monitoring Agent initialized")
    
    async def initialize(self):
        """Initialize the agent"""
        try:
            await self._load_config()
            logger.info("[SUCCESS] Options Market Monitoring Agent initialized successfully")
            return True
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize agent: {e}")
            return False
    
    async def _load_config(self):
        """Load configuration from YAML file"""
        try:
            async with aiofiles.open(self.config_path, mode="r") as f:
                content = await f.read()
                self.config = yaml.safe_load(content)
            logger.info(f"[CONFIG] Configuration loaded from {self.config_path}")
        except FileNotFoundError:
            logger.error(f"[ERROR] Config file not found at {self.config_path}")
            raise
        except Exception as e:
            logger.error(f"[ERROR] Error loading config: {e}")
            raise
    
    async def start(self, **kwargs) -> bool:
        """Start the multi-timeframe market monitoring agent"""
        try:
            logger.info("[START] Starting Options Market Monitoring Agent...")
            self.is_running = True

            # Start monitoring tasks for each timeframe
            monitoring_tasks = []

            for timeframe in self.timeframes:
                monitoring_tasks.extend([
                    self._monitor_timeframe_data(timeframe),
                    self._monitor_timeframe_greeks(timeframe),
                    self._detect_timeframe_regime(timeframe),
                    self._detect_intraday_patterns(timeframe),
                    self._extract_option_chain_intelligence(timeframe)
                ])

            # Add general monitoring tasks
            monitoring_tasks.extend([
                self._monitor_option_chains(), # This will be integrated into _load_latest_timeframe_data
                self._generate_multi_timeframe_alerts(),
                self._detect_anomalies(),
                self._generate_summary_output(),
                self._save_live_data(),
                self._log_for_ai_retraining()
            ])

            await asyncio.gather(*monitoring_tasks)

            return True
        except Exception as e:
            logger.error(f"[ERROR] Failed to start agent: {e}")
            return False
    
    async def _monitor_timeframe_data(self, timeframe: str):
        """Monitor data for specific timeframe"""
        while self.is_running:
            try:
                interval = self.config['monitoring_intervals'][timeframe]

                # Load latest data for this timeframe
                await self._load_latest_timeframe_data(timeframe)

                # Analyze data
                await self._analyze_timeframe_data(timeframe)

                logger.info(f"[MONITOR] Monitoring {timeframe} data...")
                await asyncio.sleep(interval)

            except Exception as e:
                logger.error(f"[ERROR] {timeframe} data monitoring failed: {e}")

    async def _load_latest_timeframe_data(self, timeframe: str):
        """Load latest data for specific timeframe - simplified to work with available option chain data"""
        try:
            timeframe_path = self.live_path / timeframe
            current_market_data = {}

            for underlying in self.config['underlying_symbols']:
                underlying_data = {}

                # Load option chain data (which is what we actually have)
                pattern = f"{underlying}_{timeframe}_*.parquet"
                files = list(timeframe_path.glob(pattern))

                if files:
                    latest_file = max(files, key=lambda x: x.stat().st_mtime)
                    data = pl.read_parquet(latest_file)

                    # Store as option_chain data since that's what it is
                    underlying_data['option_chain'] = data

                    # Create a synthetic index data from option chain data for compatibility
                    # We'll use the underlying price information if available
                    if 'close' in data.columns:
                        # Create a simple index-like dataframe
                        index_data = data.select([
                            'timestamp',
                            pl.col('close').alias('close'),
                            pl.col('volume').alias('volume')
                        ]).unique(subset=['timestamp']).sort('timestamp')
                        underlying_data['index'] = index_data

                    logger.debug(f"[LOAD] Loaded {timeframe} option chain data for {underlying}: {data.height} records")
                else:
                    logger.warning(f"[LOAD] No data found for {underlying} in {timeframe_path}")

                current_market_data[underlying] = underlying_data

            # Merge all loaded data into a comprehensive view
            self.market_data_cache[timeframe] = await self._merge_market_data(current_market_data, timeframe)
            self.last_update[timeframe] = datetime.now()

        except Exception as e:
            logger.error(f"[ERROR] Failed to load {timeframe} data: {e}")

    async def _merge_market_data(self, current_market_data: Dict[str, Any], timeframe: str) -> Dict[str, pl.DataFrame]:
        """Merge various market data feeds into a single comprehensive structure."""
        merged_data = {}
        for underlying, data_dict in current_market_data.items():
            if underlying in self.config['underlying_symbols']:
                # For our simplified structure, just store the data as-is
                if 'index' in data_dict:
                    merged_data[underlying] = data_dict['index']
                elif 'option_chain' in data_dict:
                    # If we only have option chain data, use that
                    merged_data[underlying] = data_dict['option_chain']
                else:
                    logger.warning(f"No usable data for {underlying} in {timeframe}")
                    continue

                logger.debug(f"[MERGE] Stored data for {underlying} in {timeframe}")

        logger.debug(f"[MERGE] Merged market data for {timeframe}")
        return merged_data

    async def _analyze_timeframe_data(self, timeframe: str):
        """Analyze data for specific timeframe"""
        try:
            for underlying in self.config['underlying_symbols']:
                if underlying in self.market_data_cache[timeframe]:
                    data = self.market_data_cache[timeframe][underlying]

                    # Check if we have the required columns for analysis
                    if data is not None and data.height > 0:
                        # Perform analysis
                        await self._analyze_price_movements(data, underlying, timeframe)
                        await self._analyze_volume_patterns(data, underlying, timeframe)
                        await self._analyze_volatility_changes(data, underlying, timeframe)
                        await self._get_time_context(timeframe) # Feature 3
                        await self._detect_anomalies_for_timeframe(data, underlying, timeframe) # Part of Feature 7
                    else:
                        logger.debug(f"[ANALYZE] No data available for {underlying} {timeframe}")

        except Exception as e:
            logger.error(f"[ERROR] Failed to analyze {timeframe} data: {e}")

    async def _analyze_price_movements(self, data: pl.DataFrame, underlying: str, timeframe: str):
        """Analyze price movements for alerts"""
        try:
            if data.height < 2:
                return

            # Calculate price changes
            latest_prices = data.tail(2)
            if latest_prices.height >= 2:
                current_price = latest_prices['close'].to_list()[-1]
                previous_price = latest_prices['close'].to_list()[-2]

                price_change = (current_price - previous_price) / previous_price

                # Check for significant price movements
                threshold = self.config['alert_thresholds']['price_change']
                if abs(price_change) > threshold:
                    alert_msg = f"🚨 [ALERT] {underlying} {timeframe}: Significant Price Change {price_change:.2%}"
                    logger.warning(alert_msg)
                    self.alerts_cache.append({"type": "price_change", "underlying": underlying, "timeframe": timeframe, "value": price_change, "timestamp": datetime.now()})

        except Exception as e:
            logger.error(f"[ERROR] Failed to analyze price movements: {e}")

    async def _analyze_volume_patterns(self, data: pl.DataFrame, underlying: str, timeframe: str):
        """Analyze volume patterns for unusual activity"""
        try:
            if data.height < 10:
                return

            # Calculate average volume
            recent_data = data.tail(10)
            avg_volume = recent_data['volume'].mean()
            latest_volume = recent_data['volume'].to_list()[-1]

            # Check for volume spikes
            volume_ratio = latest_volume / avg_volume if avg_volume > 0 else 0
            threshold = self.config['alert_thresholds']['volume_spike']

            if volume_ratio > threshold:
                alert_msg = f"📈 [ALERT] {underlying} {timeframe}: Volume Spike {volume_ratio:.2f}x"
                logger.warning(alert_msg)
                self.alerts_cache.append({"type": "volume_spike", "underlying": underlying, "timeframe": timeframe, "value": volume_ratio, "timestamp": datetime.now()})

        except Exception as e:
            logger.error(f"[ERROR] Failed to analyze volume patterns: {e}")

    async def _analyze_volatility_changes(self, data: pl.DataFrame, underlying: str, timeframe: str):
        """Analyze volatility changes"""
        try:
            if data.height < 20:
                return

            # Calculate rolling volatility
            recent_data = data.tail(20)
            returns = recent_data.with_columns([
                (pl.col('close').pct_change()).alias('returns')
            ])['returns'].drop_nulls()

            if returns.len() > 0:
                volatility = returns.std()

                # Check for volatility spikes
                threshold = self.config['alert_thresholds']['volatility_spike']
                if volatility > threshold:
                    alert_msg = f"⚡ [ALERT] {underlying} {timeframe}: Volatility Spike {volatility:.4f}"
                    logger.warning(alert_msg)
                    self.alerts_cache.append({"type": "volatility_spike", "underlying": underlying, "timeframe": timeframe, "value": volatility, "timestamp": datetime.now()})

        except Exception as e:
            logger.error(f"[ERROR] Failed to analyze volatility changes: {e}")

    async def _monitor_timeframe_greeks(self, timeframe: str):
        """Monitor Greeks for specific timeframe"""
        while self.is_running:
            try:
                interval = self.config['monitoring_intervals'][timeframe]

                # Monitor Greeks for this timeframe
                await self._calculate_timeframe_greeks(timeframe)

                logger.info(f"[GREEKS] Monitoring {timeframe} Greeks...")
                await asyncio.sleep(interval)

            except Exception as e:
                logger.error(f"[ERROR] {timeframe} Greeks monitoring failed: {e}")

    async def _calculate_timeframe_greeks(self, timeframe: str):
        """Calculate Greeks for specific timeframe"""
        try:
            # This would calculate Greeks based on current option prices
            # For now, just log the activity
            logger.debug(f"[GREEKS] Calculating Greeks for {timeframe}")
            # In a real scenario, this would use a pricing model (e.g., Black-Scholes)
            # and the option chain data from self.market_data_cache[timeframe]
            # to compute Greeks for each option contract.
            # Example:
            # for underlying, data in self.market_data_cache[timeframe].items():
            #     if 'option_chain' in data:
            #         option_data = data['option_chain']
            #         # Apply Greeks calculation logic here
            #         # e.g., option_data.with_columns(delta=calculate_delta(pl.all()))
            #         logger.debug(f"Calculated Greeks for {underlying} {timeframe} options.")

        except Exception as e:
            logger.error(f"[ERROR] Failed to calculate {timeframe} Greeks: {e}")

    async def _detect_timeframe_regime(self, timeframe: str):
        """Detect market regime for specific timeframe"""
        while self.is_running:
            try:
                interval = self.config['monitoring_intervals'][timeframe]

                # Detect market regime for this timeframe
                await self._analyze_market_regime(timeframe)

                logger.info(f"[REGIME] Detecting {timeframe} market regime...")
                await asyncio.sleep(interval)

            except Exception as e:
                logger.error(f"[ERROR] {timeframe} regime detection failed: {e}")

    async def _analyze_market_regime(self, timeframe: str):
        """Analyze market regime (Trend, Volatility, Liquidity, Momentum) for specific timeframe"""
        try:
            regime_output = {}
            for underlying in self.config['underlying_symbols']:
                if underlying in self.market_data_cache[timeframe] and 'index' in self.market_data_cache[timeframe][underlying]:
                    data = self.market_data_cache[timeframe][underlying]['index']
                    if data.height < self.config['market_regime_params']['trend_lookback']:
                        logger.debug(f"Not enough data for regime detection for {underlying} {timeframe}")
                        continue

                    # Trend detection (Bullish/Bearish/Sideways)
                    # Using SMA for simplicity, can be enhanced with ADX, etc.
                    data_with_sma = data.with_columns(
                        pt.sma(pl.col("close"), window=self.config['market_regime_params']['trend_lookback']).alias("sma")
                    )
                    current_close = data_with_sma['close'].to_list()[-1]
                    sma_value = data_with_sma['sma'].to_list()[-1]

                    trend = "sideways"
                    if current_close > sma_value * 1.005: # 0.5% above SMA
                        trend = "bullish"
                    elif current_close < sma_value * 0.995: # 0.5% below SMA
                        trend = "bearish"
                    
                    # Volatility regime (High IV, Low IV, IV Crush)
                    # Requires IV data from option chain
                    volatility_regime = "unknown"
                    if 'option_chain' in self.market_data_cache[timeframe][underlying]:
                        option_data = self.market_data_cache[timeframe][underlying]['option_chain']
                        if 'IV' in option_data.columns and option_data.height > 0:
                            avg_iv = option_data['IV'].mean()
                            # This is a simplified example. Real IV regime needs historical IV context.
                            if avg_iv > 0.25: # Example threshold for high IV
                                volatility_regime = "high_iv"
                            elif avg_iv < 0.15: # Example threshold for low IV
                                volatility_regime = "low_iv"
                            # IV Crush detection would require comparing current IV to recent historical IV after an event

                    # Liquidity regime (Low volume, spread widening)
                    liquidity_regime = "normal_liquidity"
                    avg_volume_overall = data['volume'].mean()
                    current_volume = data['volume'].to_list()[-1]
                    if current_volume < avg_volume_overall * self.config['market_regime_params']['liquidity_volume_threshold']:
                        liquidity_regime = "low_volume"
                    # Spread widening would require bid-ask spread data from option chain

                    # Momentum state (Exhaustion, acceleration)
                    # Using RSI and MACD
                    data_with_rsi = data.with_columns(
                        pt.rsi(pl.col("close"), window=14).alias("rsi")
                    )
                    current_rsi = data_with_rsi['rsi'].to_list()[-1]
                    momentum_state = "neutral_momentum"
                    if current_rsi > self.config['market_regime_params']['momentum_rsi_threshold']:
                        momentum_state = "exhaustion" # Overbought
                    elif current_rsi < 30: # Oversold
                        momentum_state = "acceleration" # Potential reversal/strong momentum

                    # Event day flagging (requires news/events feed)
                    event_day_flag = False
                    # Placeholder: if news_feed indicates major event, set event_day_flag = True

                    regime_output[underlying] = {
                        "trend": trend,
                        "volatility_regime": volatility_regime,
                        "liquidity_regime": liquidity_regime,
                        "momentum_state": momentum_state,
                        "event_day_flag": event_day_flag
                    }
                    logger.debug(f"[REGIME] Analyzed market regime for {underlying} {timeframe}: {regime_output[underlying]}")
            
            self.market_regime_state[timeframe] = regime_output
            logger.debug(f"[REGIME] Analyzing market regime for {timeframe}")

        except Exception as e:
            logger.error(f"[ERROR] Failed to analyze {timeframe} market regime: {e}")

    async def _get_time_context(self, timeframe: str):
        """Adapts behavior based on time context (Pre-market, Opening range, Mid-day lull, Power hour, Expiry)"""
        current_time = datetime.now().time()
        market_open = time(9, 15) # Indian market open
        market_close = time(15, 30) # Indian market close

        time_context = "mid_day_lull" # Default

        # Pre-market
        if current_time < market_open:
            time_context = "pre_market"
        # Opening range
        elif market_open <= current_time <= (datetime.combine(datetime.today(), market_open) + timedelta(minutes=self.config['time_aware_params']['opening_range_minutes'])).time():
            time_context = "opening_range"
        # Power hour
        elif (datetime.combine(datetime.today(), market_close) - timedelta(minutes=self.config['time_aware_params']['power_hour_start_minutes_before_close'])).time() <= current_time < market_close:
            time_context = "power_hour"
        # End-of-day reversal zone (e.g., last 15 mins)
        elif (datetime.combine(datetime.today(), market_close) - timedelta(minutes=15)).time() <= current_time < market_close:
            time_context = "end_of_day_reversal_zone"

        # Gap up/down at open (requires comparing current open with previous day's close)
        # This would need historical data, which is not directly in live cache. Placeholder.
        # gap_status = "no_gap"
        # if time_context == "opening_range":
        #     previous_day_close = ... # Load from historical data
        #     current_open = self.market_data_cache[timeframe][underlying]['index']['open'].to_list()[-1]
        #     if current_open > previous_day_close * 1.005: gap_status = "gap_up"
        #     elif current_open < previous_day_close * 0.995: gap_status = "gap_down"

        # Days to expiry for option sensitivity
        days_to_expiry = {}
        for underlying in self.config['underlying_symbols']:
            if underlying in self.market_data_cache[timeframe] and 'option_chain' in self.market_data_cache[timeframe][underlying]:
                option_data = self.market_data_cache[timeframe][underlying]['option_chain']
                if 'expiry_date' in option_data.columns and option_data.height > 0:
                    # Assuming expiry_date is a datetime object or can be converted
                    unique_expiries = option_data['expiry_date'].unique().to_list()
                    for expiry in unique_expiries:
                        if isinstance(expiry, str):
                            expiry = datetime.strptime(expiry, '%Y-%m-%d') # Adjust format as needed
                        dte = (expiry.date() - datetime.today().date()).days
                        days_to_expiry[f"{underlying}_{expiry.strftime('%Y%m%d')}"] = dte
                        if dte <= self.config['time_aware_params']['expiry_warning_days'] and dte >= 0:
                            alert_msg = f"⚠️ [ALERT] {underlying} expiry in {dte} days!"
                            logger.warning(alert_msg)
                            self.alerts_cache.append({"type": "expiry_warning", "underlying": underlying, "expiry": expiry.strftime('%Y-%m-%d'), "days_to_expiry": dte, "timestamp": datetime.now()})

        self.time_context = {
            "current_time": current_time.strftime("%H:%M:%S"),
            "market_phase": time_context,
            # "gap_status": gap_status,
            "days_to_expiry": days_to_expiry
        }
        logger.debug(f"[TIME] Time context for {timeframe}: {self.time_context}")

    async def _detect_intraday_patterns(self, timeframe: str):
        """Detects setups in real time (Breakouts, Consolidation, OI shifts, VWAP, RSI/MACD, OTM/IV spikes)"""
        while self.is_running:
            try:
                interval = self.config['monitoring_intervals'][timeframe]
                for underlying in self.config['underlying_symbols']:
                    if underlying in self.market_data_cache[timeframe] and 'index' in self.market_data_cache[timeframe][underlying]:
                        data = self.market_data_cache[timeframe][underlying]['index']
                        if data.height < 30: # Need sufficient data for patterns
                            logger.debug(f"Not enough data for intraday pattern detection for {underlying} {timeframe}")
                            continue

                        # Breakouts (Price, Volume)
                        await self._detect_breakouts(data, underlying, timeframe)

                        # Consolidation zones (e.g., low ATR, tight range)
                        await self._detect_consolidation(data, underlying, timeframe)

                        # VWAP bounce/rejection (requires VWAP calculation)
                        await self._detect_vwap_interaction(data, underlying, timeframe)

                        # RSI divergence, MACD cross
                        await self._detect_technical_divergence(data, underlying, timeframe)

                    if underlying in self.market_data_cache[timeframe] and 'option_chain' in self.market_data_cache[timeframe][underlying]:
                        option_data = self.market_data_cache[timeframe][underlying]['option_chain']
                        # OI shift zones (support/resistance shift)
                        await self._detect_oi_shift_zones(option_data, underlying, timeframe)
                        # OTM spiking, IV explosions
                        await self._detect_otm_iv_spikes(option_data, underlying, timeframe)

                logger.info(f"[PATTERN] Detecting {timeframe} intraday patterns...")
                await asyncio.sleep(interval)

            except Exception as e:
                logger.error(f"[ERROR] {timeframe} intraday pattern detection failed: {e}")

    async def _detect_breakouts(self, data: pl.DataFrame, underlying: str, timeframe: str):
        """Detects price and volume breakouts"""
        try:
            # Simple breakout: current close > highest high of last N periods
            lookback = 20 # Example lookback
            if data.height < lookback: return

            recent_highs = data.tail(lookback)['high']
            current_close = data['close'].to_list()[-1]
            current_volume = data['volume'].to_list()[-1]

            if current_close > recent_highs.max() and current_volume > data['volume'].mean() * self.config['alert_thresholds']['volume_spike']:
                alert_msg = f"🚀 [ALERT] {underlying} {timeframe}: Price & Volume Breakout Detected!"
                logger.warning(alert_msg)
                self.alerts_cache.append({"type": "breakout", "underlying": underlying, "timeframe": timeframe, "timestamp": datetime.now()})
        except Exception as e:
            logger.error(f"[ERROR] Breakout detection failed: {e}")

    async def _detect_consolidation(self, data: pl.DataFrame, underlying: str, timeframe: str):
        """Detects consolidation zones (e.g., low ATR)"""
        try:
            lookback = 14 # ATR period
            if data.height < lookback: return

            data_with_atr = data.with_columns(
                pt.atr(high=pl.col("high"), low=pl.col("low"), close=pl.col("close"), window=lookback).alias("atr")
            )
            current_atr = data_with_atr['atr'].to_list()[-1]
            avg_atr = data_with_atr['atr'].mean()

            if current_atr < avg_atr * 0.7: # ATR significantly lower than average
                logger.info(f"📊 [INFO] {underlying} {timeframe}: Potential Consolidation Zone (Low ATR)")
        except Exception as e:
            logger.error(f"[ERROR] Consolidation detection failed: {e}")

    async def _detect_vwap_interaction(self, data: pl.DataFrame, underlying: str, timeframe: str):
        """Detects VWAP bounce/rejection"""
        try:
            # VWAP calculation: (Typical Price * Volume) / Volume
            # Requires 'open', 'high', 'low', 'close', 'volume' columns
            if not all(col in data.columns for col in ['open', 'high', 'low', 'close', 'volume']):
                logger.debug(f"Missing columns for VWAP calculation in {underlying} {timeframe}")
                return
            
            data_with_vwap = data.with_columns([
                ((pl.col("high") + pl.col("low") + pl.col("close")) / 3 * pl.col("volume")).alias("typical_price_volume"),
                pl.col("volume").alias("volume_sum")
            ]).with_columns([
                pl.col("typical_price_volume").cumsum().alias("cum_typical_price_volume"),
                pl.col("volume_sum").cumsum().alias("cum_volume")
            ]).with_columns(
                (pl.col("cum_typical_price_volume") / pl.col("cum_volume")).alias("vwap")
            )

            if data_with_vwap.height < 2: return

            current_close = data_with_vwap['close'].to_list()[-1]
            previous_close = data_with_vwap['close'].to_list()[-2]
            current_vwap = data_with_vwap['vwap'].to_list()[-1]
            previous_vwap = data_with_vwap['vwap'].to_list()[-2]

            # Simple bounce/rejection logic
            if previous_close < previous_vwap and current_close > current_vwap:
                alert_msg = f"⬆️ [ALERT] {underlying} {timeframe}: VWAP Bounce Detected!"
                logger.warning(alert_msg)
                self.alerts_cache.append({"type": "vwap_bounce", "underlying": underlying, "timeframe": timeframe, "timestamp": datetime.now()})
            elif previous_close > previous_vwap and current_close < current_vwap:
                alert_msg = f"⬇️ [ALERT] {underlying} {timeframe}: VWAP Rejection Detected!"
                logger.warning(alert_msg)
                self.alerts_cache.append({"type": "vwap_rejection", "underlying": underlying, "timeframe": timeframe, "timestamp": datetime.now()})

        except Exception as e:
            logger.error(f"[ERROR] VWAP interaction detection failed: {e}")

    async def _detect_technical_divergence(self, data: pl.DataFrame, underlying: str, timeframe: str):
        """Detects RSI divergence and MACD cross"""
        try:
            if data.height < 30: return # Need enough data for indicators

            # RSI Divergence (simplified: price makes new low, RSI makes higher low)
            data_with_rsi = data.with_columns(
                pt.rsi(pl.col("close"), window=14).alias("rsi")
            )
            if data_with_rsi.height >= 5: # Check last few points
                recent_closes = data_with_rsi['close'].tail(5).to_list()
                recent_rsis = data_with_rsi['rsi'].tail(5).to_list()

                # Bearish divergence: Price higher high, RSI lower high
                if recent_closes[-1] > recent_closes[0] and recent_rsis[-1] < recent_rsis[0]:
                    logger.info(f"📉 [INFO] {underlying} {timeframe}: Bearish RSI Divergence")
                # Bullish divergence: Price lower low, RSI higher low
                elif recent_closes[-1] < recent_closes[0] and recent_rsis[-1] > recent_rsis[0]:
                    logger.info(f"📈 [INFO] {underlying} {timeframe}: Bullish RSI Divergence")

            # MACD Cross
            data_with_macd = data.with_columns(
                pt.macd(pl.col("close"), fast_period=12, slow_period=26, signal_period=self.config['market_regime_params']['momentum_macd_signal_cross_period']).alias("macd")
            )
            if "macd_signal" in data_with_macd.columns and data_with_macd.height >= 2:
                current_macd = data_with_macd['macd'].to_list()[-1]
                previous_macd = data_with_macd['macd'].to_list()[-2]
                current_signal = data_with_macd['macd_signal'].to_list()[-1]
                previous_signal = data_with_macd['macd_signal'].to_list()[-2]

                if previous_macd < previous_signal and current_macd > current_signal:
                    alert_msg = f"⬆️ [ALERT] {underlying} {timeframe}: Bullish MACD Cross!"
                    logger.warning(alert_msg)
                    self.alerts_cache.append({"type": "macd_cross_bullish", "underlying": underlying, "timeframe": timeframe, "timestamp": datetime.now()})
                elif previous_macd > previous_signal and current_macd < current_signal:
                    alert_msg = f"⬇️ [ALERT] {underlying} {timeframe}: Bearish MACD Cross!"
                    logger.warning(alert_msg)
                    self.alerts_cache.append({"type": "macd_cross_bearish", "underlying": underlying, "timeframe": timeframe, "timestamp": datetime.now()})

        except Exception as e:
            logger.error(f"[ERROR] Technical divergence detection failed: {e}")

    async def _extract_option_chain_intelligence(self, timeframe: str):
        """Extracts intelligence from real-time option chain data"""
        while self.is_running:
            try:
                interval = self.config['monitoring_intervals'][timeframe]
                for underlying in self.config['underlying_symbols']:
                    if underlying in self.market_data_cache[timeframe] and 'option_chain' in self.market_data_cache[timeframe][underlying]:
                        option_data = self.market_data_cache[timeframe][underlying]['option_chain']
                        if option_data.height == 0:
                            logger.debug(f"No option chain data for {underlying} {timeframe}")
                            continue

                        # Detect long buildup / short covering / unwinding
                        await self._analyze_oi_changes(option_data, underlying, timeframe)

                        # Monitor OI clusters, OI change heatmap
                        await self._monitor_oi_clusters(option_data, underlying, timeframe)

                        # Track IV skew and Put-Call Ratio (PCR)
                        await self._analyze_iv_skew_pcr(option_data, underlying, timeframe)

                        # Compute max pain zone movement
                        await self._compute_max_pain(option_data, underlying, timeframe)

                        # Analyze Bid-Ask pressure (buyer/seller dominance)
                        await self._analyze_bid_ask_pressure(option_data, underlying, timeframe)

                logger.info(f"[OPTIONS] Extracting {timeframe} option chain intelligence...")
                await asyncio.sleep(interval)

            except Exception as e:
                logger.error(f"[ERROR] {timeframe} option chain intelligence extraction failed: {e}")

    async def _analyze_oi_changes(self, option_data: pl.DataFrame, underlying: str, timeframe: str):
        """Detects long buildup / short covering / unwinding based on OI and price changes"""
        try:
            # This requires historical OI data to compare current OI with previous.
            # For a real-time system, you'd need to store previous snapshots of option chain.
            # Placeholder for logic:
            # if current_oi > previous_oi and price_up: long buildup
            # if current_oi < previous_oi and price_up: short covering
            # if current_oi > previous_oi and price_down: short buildup
            # if current_oi < previous_oi and price_down: long unwinding

            # For now, a simplified check for significant OI change
            if 'OI' in option_data.columns and option_data.height > 1:
                latest_oi = option_data['OI'].to_list()[-1]
                previous_oi = option_data['OI'].to_list()[-2] # This assumes sorted by time and same contract
                
                oi_change_percent = (latest_oi - previous_oi) / previous_oi if previous_oi != 0 else 0

                if abs(oi_change_percent) > self.config['alert_thresholds']['oi_spike_threshold']:
                    alert_msg = f"📊 [ALERT] {underlying} {timeframe}: Significant OI Change {oi_change_percent:.2%}"
                    logger.warning(alert_msg)
                    self.alerts_cache.append({"type": "oi_change", "underlying": underlying, "timeframe": timeframe, "value": oi_change_percent, "timestamp": datetime.now()})

        except Exception as e:
            logger.error(f"[ERROR] OI change analysis failed: {e}")

    async def _monitor_oi_clusters(self, option_data: pl.DataFrame, underlying: str, timeframe: str):
        """Monitors OI clusters and OI change heatmap"""
        try:
            if 'strike_price' in option_data.columns and 'OI' in option_data.columns:
                # Group by strike price and sum OI for calls and puts separately
                calls_oi = option_data.filter(pl.col('option_type') == 'CE').group_by('strike_price').agg(pl.col('OI').sum().alias('total_oi'))
                puts_oi = option_data.filter(pl.col('option_type') == 'PE').group_by('strike_price').agg(pl.col('OI').sum().alias('total_oi'))

                # Identify top OI clusters
                top_call_oi = calls_oi.sort('total_oi', descending=True).head(3)
                top_put_oi = puts_oi.sort('total_oi', descending=True).head(3)

                logger.debug(f"Top Call OI Clusters for {underlying} {timeframe}:\n{top_call_oi}")
                logger.debug(f"Top Put OI Clusters for {underlying} {timeframe}:\n{top_put_oi}")

                # OI change heatmap would require comparing current OI with previous snapshots
                # This would involve storing historical OI data and calculating deltas per strike.

        except Exception as e:
            logger.error(f"[ERROR] OI cluster monitoring failed: {e}")

    async def _analyze_iv_skew_pcr(self, option_data: pl.DataFrame, underlying: str, timeframe: str):
        """Tracks IV skew and Put-Call Ratio (PCR)"""
        try:
            if 'IV' in option_data.columns and 'option_type' in option_data.columns and 'OI' in option_data.columns:
                # IV Skew: Compare IV of OTM puts vs OTM calls for same expiry/moneyness
                # This is complex and requires a proper volatility surface construction.
                # Placeholder:
                # atm_strike = ... # Determine ATM strike based on current underlying price
                # otm_call_iv = option_data.filter((pl.col('option_type') == 'CE') & (pl.col('strike_price') > atm_strike))['IV'].mean()
                # otm_put_iv = option_data.filter((pl.col('option_type') == 'PE') & (pl.col('strike_price') < atm_strike))['IV'].mean()
                # iv_skew = otm_put_iv - otm_call_iv
                # logger.debug(f"IV Skew for {underlying} {timeframe}: {iv_skew:.4f}")

                # Put-Call Ratio (PCR) based on Open Interest
                total_call_oi = option_data.filter(pl.col('option_type') == 'CE')['OI'].sum()
                total_put_oi = option_data.filter(pl.col('option_type') == 'PE')['OI'].sum()

                pcr = total_put_oi / total_call_oi if total_call_oi > 0 else 0
                logger.debug(f"PCR (OI) for {underlying} {timeframe}: {pcr:.2f}")

                # Alert on significant PCR spike (example threshold)
                if pcr > 1.35: # Example: high PCR suggests bullish sentiment
                    alert_msg = f"📈 [ALERT] {underlying} {timeframe}: PCR Spike at {pcr:.2f}!"
                    logger.warning(alert_msg)
                    self.alerts_cache.append({"type": "pcr_spike", "underlying": underlying, "timeframe": timeframe, "value": pcr, "timestamp": datetime.now()})
                elif pcr < 0.70: # Example: low PCR suggests bearish sentiment
                    alert_msg = f"📉 [ALERT] {underlying} {timeframe}: PCR Drop at {pcr:.2f}!"
                    logger.warning(alert_msg)
                    self.alerts_cache.append({"type": "pcr_drop", "underlying": underlying, "timeframe": timeframe, "value": pcr, "timestamp": datetime.now()})

        except Exception as e:
            logger.error(f"[ERROR] IV skew and PCR analysis failed: {e}")

    async def _compute_max_pain(self, option_data: pl.DataFrame, underlying: str, timeframe: str):
        """Computes max pain zone movement"""
        try:
            if 'strike_price' in option_data.columns and 'OI' in option_data.columns and 'option_type' in option_data.columns:
                # Max Pain calculation: The strike price at which option buyers would suffer the maximum financial loss.
                # This involves iterating through strike prices and calculating total OI loss for calls and puts.
                # Simplified approach:
                strikes = option_data['strike_price'].unique().sort().to_list()
                max_pain_strike = None
                min_loss = float('inf')

                for strike in strikes:
                    # Loss for call writers (if price > strike, calls are ITM)
                    call_oi_above_strike = option_data.filter((pl.col('option_type') == 'CE') & (pl.col('strike_price') > strike))['OI'].sum()
                    # Loss for put writers (if price < strike, puts are ITM)
                    put_oi_below_strike = option_data.filter((pl.col('option_type') == 'PE') & (pl.col('strike_price') < strike))['OI'].sum()
                    
                    total_loss_at_strike = call_oi_above_strike + put_oi_below_strike

                    if total_loss_at_strike < min_loss:
                        min_loss = total_loss_at_strike
                        max_pain_strike = strike
                
                if max_pain_strike:
                    logger.debug(f"Max Pain for {underlying} {timeframe}: {max_pain_strike}")
                    # Track movement by comparing with previous max pain (requires historical data)

        except Exception as e:
            logger.error(f"[ERROR] Max pain computation failed: {e}")

    async def _analyze_bid_ask_pressure(self, option_data: pl.DataFrame, underlying: str, timeframe: str):
        """Analyzes Bid-Ask pressure (buyer/seller dominance)"""
        try:
            if 'bid' in option_data.columns and 'ask' in option_data.columns and 'volume' in option_data.columns:
                # Simple bid-ask pressure: Compare volume at bid vs volume at ask
                # This typically requires tick-level data, not just aggregated bars.
                # For bar data, we can infer from close price relative to bid/ask.
                # If close is closer to ask, more buying pressure. If closer to bid, more selling pressure.
                
                # Placeholder for more sophisticated analysis:
                # total_bid_volume = option_data['bid_volume'].sum() # Assuming bid_volume column exists
                # total_ask_volume = option_data['ask_volume'].sum() # Assuming ask_volume column exists
                # bid_ask_ratio = total_bid_volume / total_ask_volume if total_ask_volume > 0 else 0
                # logger.debug(f"Bid-Ask Pressure Ratio for {underlying} {timeframe}: {bid_ask_ratio:.2f}")

                # For now, just log the presence of bid/ask data
                logger.debug(f"Bid/Ask data available for {underlying} {timeframe}. Advanced pressure analysis can be implemented.")

        except Exception as e:
            logger.error(f"[ERROR] Bid-Ask pressure analysis failed: {e}")

    async def _monitor_option_chains(self):
        """Monitor option chains across all timeframes"""
        # This function is now largely integrated into _load_latest_timeframe_data
        # and _extract_option_chain_intelligence.
        # Keeping it as a placeholder for any future high-level option chain specific tasks.
        while self.is_running:
            try:
                logger.info("[MONITOR] Option chain monitoring loop active (integrated).")
                await asyncio.sleep(60) # Sleep to prevent busy-waiting
            except Exception as e:
                logger.error(f"[ERROR] Option chain monitoring failed: {e}")

    async def _generate_multi_timeframe_alerts(self):
        """Generate alerts based on multi-timeframe analysis and pre-validation"""
        while self.is_running:
            try:
                # Generate cross-timeframe alerts
                await self._check_cross_timeframe_signals()
                await self._pre_validate_signals_and_alert() # Feature 6
                await self._feedback_loop_to_agents() # Feature 10

                logger.info("[ALERT] Checking multi-timeframe alerts...")
                await asyncio.sleep(60)  # Check every minute

            except Exception as e:
                logger.error(f"[ERROR] Multi-timeframe alert generation failed: {e}")

    async def _check_cross_timeframe_signals(self):
        """Check for signals across multiple timeframes"""
        try:
            # This would check for confluence across timeframes
            # Example: If 1min shows bullish breakout and 15min shows bullish trend
            # For now, just log the activity
            logger.debug("[SIGNAL] Checking cross-timeframe signals...")
            # Iterate through self.market_data_cache for different timeframes
            # and compare indicators/patterns.
            # Example:
            # if self.market_regime_state['1min'].get('NIFTY', {}).get('trend') == 'bullish' and \
            #    self.market_regime_state['15min'].get('NIFTY', {}).get('trend') == 'bullish':
            #    logger.info("Bullish confluence across 1min and 15min for NIFTY!")
            #    self.alerts_cache.append({"type": "confluence", "message": "Bullish confluence", "timestamp": datetime.now()})

        except Exception as e:
            logger.error(f"[ERROR] Failed to check cross-timeframe signals: {e}")

    async def _pre_validate_signals_and_alert(self):
        """Warns Signal Agent if market regime is incompatible, volatility is outside range, etc."""
        try:
            # This function would receive potential signals from a Signal Agent (or generate them internally)
            # and apply pre-validation rules.
            
            # Example: Check if current market regime is compatible with a hypothetical strategy
            current_regime = self.market_regime_state.get('15min', {}).get('NIFTY', {}) # Using 15min for overall regime
            
            if current_regime:
                market_regime_type = current_regime.get('trend', 'unknown')
                volatility_regime = current_regime.get('volatility_regime', 'unknown')

                # Example strategy compatibility check
                if market_regime_type == "sideways" and "breakout" in [a['type'] for a in self.alerts_cache if 'type' in a]:
                    alert_msg = "⚠️ [PRE-VALIDATION] Market is sideways, breakout strategy might be incompatible."
                    logger.warning(alert_msg)
                    self.strategy_suppression_decisions.append({"reason": "sideways_breakout_incompatible", "timestamp": datetime.now()})
                    self.alerts_cache.append({"type": "strategy_restriction", "message": "disable_strat_002", "timestamp": datetime.now()})

                # Volatility check
                if volatility_regime == "high_iv":
                    alert_msg = "⚠️ [PRE-VALIDATION] High IV detected, consider reducing capital or using IV-neutral strategies."
                    logger.warning(alert_msg)
                    self.alerts_cache.append({"type": "strategy_restriction", "message": "reduce capital by 20%", "timestamp": datetime.now()})
                
                # Price at news-sensitive level (requires news feed integration)
                # Placeholder: if news_event_active and price_near_key_level:
                #    self.alerts_cache.append({"type": "risk_on_off", "signal": "risk-off", "reason": "news_sensitive_level", "timestamp": datetime.now()})

            # Send "risk-on / risk-off" signal for capital modulation
            # This would be based on an aggregation of all risk factors
            overall_risk_signal = "risk-on"
            if len(self.strategy_suppression_decisions) > 0 or len([a for a in self.alerts_cache if a['type'] == 'anomaly']) > 0:
                overall_risk_signal = "risk-off"
            
            if overall_risk_signal == "risk-off":
                alert_msg = "🛑 [RISK SIGNAL] Sending 'risk-off' signal for capital modulation."
                logger.warning(alert_msg)
                self.alerts_cache.append({"type": "risk_on_off", "signal": "risk-off", "timestamp": datetime.now()})
            else:
                logger.info("✅ [RISK SIGNAL] Market conditions are 'risk-on'.")

        except Exception as e:
            logger.error(f"[ERROR] Signal pre-validation and alerting failed: {e}")

    async def _detect_anomalies(self):
        """Detects various market anomalies"""
        while self.is_running:
            try:
                # Anomalies are often detected at the lowest timeframe (e.g., 1min)
                # and then aggregated or flagged for higher timeframes.
                # The actual detection logic is in _detect_anomalies_for_timeframe
                # This loop ensures the anomaly detection runs periodically.
                logger.info("[ANOMALY] Checking for anomalies...")
                await asyncio.sleep(self.config['monitoring_intervals']['1min']) # Check frequently

            except Exception as e:
                logger.error(f"[ERROR] Anomaly detection failed: {e}")

    async def _detect_anomalies_for_timeframe(self, data: pl.DataFrame, underlying: str, timeframe: str):
        """Detects anomalies within a specific timeframe's data"""
        try:
            # Abnormal spread (requires bid/ask data)
            if 'bid' in data.columns and 'ask' in data.columns:
                data_with_spread = data.with_columns(
                    (pl.col('ask') - pl.col('bid')).alias('spread')
                )
                if data_with_spread.height > 10:
                    current_spread = data_with_spread['spread'].to_list()[-1]
                    avg_spread = data_with_spread['spread'].mean()
                    if current_spread > avg_spread * self.config['anomaly_detection_params']['spread_deviation_multiplier']:
                        anomaly_msg = f"⚠️ [ANOMALY] {underlying} {timeframe}: Abnormal Spread Detected ({current_spread:.2f})"
                        logger.warning(anomaly_msg)
                        self.anomaly_logs.append({"type": "abnormal_spread", "underlying": underlying, "timeframe": timeframe, "value": current_spread, "timestamp": datetime.now()})
                        self.alerts_cache.append({"type": "anomaly", "message": anomaly_msg, "timestamp": datetime.now()})

            # Sudden OI spike without price move (requires OI and price data)
            if 'OI' in data.columns and 'close' in data.columns and data.height > 2:
                current_oi = data['OI'].to_list()[-1]
                previous_oi = data['OI'].to_list()[-2]
                current_price = data['close'].to_list()[-1]
                previous_price = data['close'].to_list()[-2]

                oi_change_percent = (current_oi - previous_oi) / previous_oi if previous_oi != 0 else 0
                price_change_percent = (current_price - previous_price) / previous_price if previous_price != 0 else 0

                if abs(oi_change_percent) > self.config['alert_thresholds']['oi_spike_threshold'] and \
                   abs(price_change_percent) < self.config['anomaly_detection_params']['oi_price_divergence_threshold']:
                    anomaly_msg = f"⚠️ [ANOMALY] {underlying} {timeframe}: Sudden OI Spike without Price Move (OI: {oi_change_percent:.2%}, Price: {price_change_percent:.2%})"
                    logger.warning(anomaly_msg)
                    self.anomaly_logs.append({"type": "oi_price_divergence", "underlying": underlying, "timeframe": timeframe, "oi_change": oi_change_percent, "price_change": price_change_percent, "timestamp": datetime.now()})
                    self.alerts_cache.append({"type": "anomaly", "message": anomaly_msg, "timestamp": datetime.now()})

            # Unusual IV moves not justified by price (requires IV and price data)
            if 'IV' in data.columns and 'close' in data.columns and data.height > 2:
                current_iv = data['IV'].to_list()[-1]
                previous_iv = data['IV'].to_list()[-2]
                current_price = data['close'].to_list()[-1]
                previous_price = data['close'].to_list()[-2]

                iv_change_percent = (current_iv - previous_iv) / previous_iv if previous_iv != 0 else 0
                price_change_percent = (current_price - previous_price) / previous_price if previous_price != 0 else 0

                if abs(iv_change_percent) > self.config['alert_thresholds']['iv_explosion_threshold'] and \
                   abs(price_change_percent) < self.config['anomaly_detection_params']['iv_price_divergence_threshold']:
                    anomaly_msg = f"⚠️ [ANOMALY] {underlying} {timeframe}: Unusual IV Move without Price Justification (IV: {iv_change_percent:.2%}, Price: {price_change_percent:.2%})"
                    logger.warning(anomaly_msg)
                    self.anomaly_logs.append({"type": "iv_price_divergence", "underlying": underlying, "timeframe": timeframe, "iv_change": iv_change_percent, "price_change": price_change_percent, "timestamp": datetime.now()})
                    self.alerts_cache.append({"type": "anomaly", "message": anomaly_msg, "timestamp": datetime.now()})

            # Algo spikes / spoofing hints (rapid depth changes) - requires order book depth data
            # Placeholder: if order_book_depth_change > threshold:
            #    anomaly_msg = f"⚠️ [ANOMALY] {underlying} {timeframe}: Algo Spike/Spoofing Hint (Rapid Depth Change)"
            #    logger.warning(anomaly_msg)
            #    self.anomaly_logs.append({"type": "algo_spoofing", "underlying": underlying, "timeframe": timeframe, "timestamp": datetime.now()})
            #    self.alerts_cache.append({"type": "anomaly", "message": anomaly_msg, "timestamp": datetime.now()})

            # Historical vs live metric drift (slippage, reaction time) - requires comparison with historical performance
            # Placeholder: if live_slippage > historical_avg_slippage * multiplier:
            #    anomaly_msg = f"⚠️ [ANOMALY] {underlying} {timeframe}: Live Metric Drift (Slippage)"
            #    logger.warning(anomaly_msg)
            #    self.anomaly_logs.append({"type": "metric_drift", "underlying": underlying, "timeframe": timeframe, "timestamp": datetime.now()})
            #    self.alerts_cache.append({"type": "anomaly", "message": anomaly_msg, "timestamp": datetime.now()})

        except Exception as e:
            logger.error(f"[ERROR] Anomaly detection for timeframe {timeframe} failed: {e}")

    async def _generate_summary_output(self):
        """Creates real-time snapshot summary (JSON and natural language) using technical indicators"""
        while self.is_running:
            try:
                summary = {
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "market_regime": "unknown",
                    "volatility_regime": "unknown",
                    "recommended_assets": [],
                    "strategy_restrictions": [],
                    "alerts": []
                }

                # Get real market conditions from technical indicators
                main_underlying = self.config['underlying_symbols'][0] if self.config['underlying_symbols'] else 'NIFTY'
                market_conditions = self.indicators_manager.get_market_conditions(main_underlying)

                if market_conditions:
                    # Use real market regime from technical analysis
                    summary["market_regime"] = f"{market_conditions.regime.value}_{market_conditions.momentum_state}"
                    summary["volatility_regime"] = "high_vol" if market_conditions.volatility_level > 0.25 else "low_vol"

                    # Add trend strength information
                    summary["trend_strength"] = market_conditions.trend_strength
                    summary["support_level"] = market_conditions.support_level
                    summary["resistance_level"] = market_conditions.resistance_level
                else:
                    # Fallback to old method if technical indicators not available
                    logger.warning("[WARNING] No technical indicators data available, using fallback")

                # Add recommended assets
                summary["recommended_assets"] = self.config['underlying_symbols']

                # Add strategy restrictions from alerts cache
                for alert in self.alerts_cache:
                    if alert['type'] == 'strategy_restriction':
                        summary["strategy_restrictions"].append(alert['message'])
                    elif alert['type'] == 'risk_on_off':
                        summary["strategy_restrictions"].append(f"capital_modulation: {alert['signal']}")
                    elif alert['type'] == 'oi_change' or alert['type'] == 'pcr_spike' or alert['type'] == 'pcr_drop':
                        summary["alerts"].append(f"{alert['type']} detected near {alert.get('underlying', '')} at {alert.get('value', '')}")
                    elif alert['type'] == 'anomaly':
                        summary["alerts"].append(alert['message'])
                    elif alert['type'] == 'expiry_warning':
                        summary["alerts"].append(f"Expiry warning for {alert['underlying']} in {alert['days_to_expiry']} days.")
                    elif alert['type'] == 'breakout':
                        summary["alerts"].append(f"Breakout detected for {alert['underlying']} {alert['timeframe']}")
                    elif alert['type'] == 'vwap_bounce':
                        summary["alerts"].append(f"VWAP Bounce detected for {alert['underlying']} {alert['timeframe']}")
                    elif alert['type'] == 'vwap_rejection':
                        summary["alerts"].append(f"VWAP Rejection detected for {alert['underlying']} {alert['timeframe']}")
                    elif alert['type'] == 'macd_cross_bullish':
                        summary["alerts"].append(f"Bullish MACD Cross for {alert['underlying']} {alert['timeframe']}")
                    elif alert['type'] == 'macd_cross_bearish':
                        summary["alerts"].append(f"Bearish MACD Cross for {alert['underlying']} {alert['timeframe']}")
                    elif alert['type'] == 'volatility_spike':
                        summary["alerts"].append(f"Volatility spike for {alert['underlying']} {alert['timeframe']}")
                    elif alert['type'] == 'volume_spike':
                        summary["alerts"].append(f"Volume spike for {alert['underlying']} {alert['timeframe']}")
                    elif alert['type'] == 'price_change':
                        summary["alerts"].append(f"Significant price change for {alert['underlying']} {alert['timeframe']}")

                # Clear alerts cache after generating summary
                self.alerts_cache.clear()

                # Save JSON summary
                output_path = Path(self.config['output_settings']['summary_output_path'])
                output_path.parent.mkdir(parents=True, exist_ok=True)
                async with aiofiles.open(output_path, mode="w") as f:
                    await f.write(json.dumps(summary, indent=2))
                logger.info(f"📊 [OUTPUT] Market summary saved to {output_path}")

                # Translate into natural language for LLM Interface Agent
                natural_language_summary = self._translate_summary_to_natural_language(summary)
                logger.info(f"🗣️ [LLM] Natural Language Summary: {natural_language_summary}")

                await asyncio.sleep(60) # Generate summary every minute

            except Exception as e:
                logger.error(f"[ERROR] Summary output generation failed: {e}")

    def _translate_summary_to_natural_language(self, summary: Dict[str, Any]) -> str:
        """Translates the JSON summary into natural language using technical analysis data."""
        nl_summary = f"Market snapshot at {summary['timestamp']}:\n"

        # Parse market regime
        market_regime_parts = summary['market_regime'].split('_')
        regime = market_regime_parts[0] if market_regime_parts else "unknown"
        momentum = market_regime_parts[1] if len(market_regime_parts) > 1 else "neutral"

        # Create more descriptive market description
        regime_descriptions = {
            "trending-bull": "in a strong bullish trend",
            "trending-bear": "in a strong bearish trend",
            "sideways-low-vol": "moving sideways with low volatility",
            "sideways-high-vol": "moving sideways with high volatility",
            "volatile-uncertain": "experiencing high volatility with uncertain direction",
            "breakout": "showing breakout patterns"
        }

        regime_desc = regime_descriptions.get(regime, regime.replace('_', ' '))
        volatility_desc = summary.get('volatility_regime', 'unknown').replace('_', ' ')

        nl_summary += f"The market is currently {regime_desc} with {volatility_desc} volatility"

        # Add trend strength if available
        if 'trend_strength' in summary:
            strength = abs(summary['trend_strength'])
            if strength > 0.6:
                nl_summary += f" and strong momentum ({summary['trend_strength']:.2f})"
            elif strength > 0.3:
                nl_summary += f" and moderate momentum ({summary['trend_strength']:.2f})"
            else:
                nl_summary += f" and weak momentum ({summary['trend_strength']:.2f})"

        nl_summary += ".\n"

        # Add support/resistance levels if available
        if 'support_level' in summary and 'resistance_level' in summary:
            nl_summary += f"Key levels: Support at ₹{summary['support_level']:.0f}, Resistance at ₹{summary['resistance_level']:.0f}.\n"

        if summary['recommended_assets']:
            nl_summary += f"Recommended assets for focus: {', '.join(summary['recommended_assets'])}.\n"

        if summary['strategy_restrictions']:
            nl_summary += "Strategy adjustments advised: " + ", ".join(summary['strategy_restrictions']) + ".\n"
        
        if summary['alerts']:
            nl_summary += "Active alerts: " + "; ".join(summary['alerts']) + ".\n"
        
        return nl_summary

    async def _log_for_ai_retraining(self):
        """Stores live regime tags, anomaly logs, strategy suppression decisions for AI retraining"""
        while self.is_running:
            try:
                if self.config['output_settings']['log_retraining_data']:
                    log_data = []
                    timestamp = datetime.now()

                    # Log market regime state
                    for tf, regimes in self.market_regime_state.items():
                        for underlying, regime_info in regimes.items():
                            log_data.append({
                                "timestamp": timestamp,
                                "type": "market_regime",
                                "timeframe": tf,
                                "underlying": underlying,
                                **regime_info
                            })
                    
                    # Log anomalies
                    for anomaly in self.anomaly_logs:
                        log_data.append({
                            "timestamp": timestamp,
                            "type": "anomaly_log",
                            **anomaly
                        })
                    self.anomaly_logs.clear() # Clear after logging

                    # Log strategy suppression decisions
                    for decision in self.strategy_suppression_decisions:
                        log_data.append({
                            "timestamp": timestamp,
                            "type": "strategy_suppression",
                            **decision
                        })
                    self.strategy_suppression_decisions.clear() # Clear after logging

                    if log_data:
                        log_df = pl.DataFrame(log_data)
                        retraining_path = Path(self.config['output_settings']['retraining_data_path'])
                        retraining_path.parent.mkdir(parents=True, exist_ok=True)

                        # Append to existing parquet or create new
                        if retraining_path.exists():
                            existing_df = pl.read_parquet(retraining_path)
                            log_df = pl.concat([existing_df, log_df])
                        
                        log_df.write_parquet(retraining_path)
                        logger.info(f"💾 [LOG] Logged {len(log_data)} entries for AI retraining to {retraining_path}")

                await asyncio.sleep(300) # Log every 5 minutes

            except Exception as e:
                logger.error(f"[ERROR] AI retraining logging failed: {e}")

    async def _feedback_loop_to_agents(self):
        """Signals other agents (Execution, Risk Management) for actions"""
        # This function would interact with other agents, e.g., via a message queue or direct calls.
        # For now, it will log the signals it would send.
        try:
            # Example: Pause trading if extreme volatility
            for tf, regimes in self.market_regime_state.items():
                for underlying, regime_info in regimes.items():
                    if regime_info.get('volatility_regime') == 'high_iv':
                        logger.warning(f"🛑 [FEEDBACK] Signaling Execution Agent: Pause trading for {underlying} due to extreme volatility!")
                        # In a real system: send_signal_to_execution_agent("pause_trading", underlying)
                        self.alerts_cache.append({"type": "feedback_signal", "agent": "Execution", "action": "pause_trading", "underlying": underlying, "timestamp": datetime.now()})

            # Example: Reduce exposure post rapid drawdown (requires drawdown detection)
            # Placeholder: if rapid_drawdown_detected:
            #    logger.warning(f"📉 [FEEDBACK] Signaling Risk Management Agent: Reduce exposure!")
            #    self.alerts_cache.append({"type": "feedback_signal", "agent": "RiskManagement", "action": "reduce_exposure", "timestamp": datetime.now()})

            # Example: Re-enable strategy post regime normalization
            # Placeholder: if regime_normalized:
            #    logger.info(f"✅ [FEEDBACK] Signaling Execution Agent: Re-enable strategy!")
            #    self.alerts_cache.append({"type": "feedback_signal", "agent": "Execution", "action": "re_enable_strategy", "timestamp": datetime.now()})

            # Recommends capital allocation adjustments
            for alert in self.alerts_cache:
                if alert['type'] == 'strategy_restriction' and "capital" in alert['message']:
                    logger.info(f"💰 [FEEDBACK] Signaling Risk Management Agent: Capital adjustment - {alert['message']}")
                    self.alerts_cache.append({"type": "feedback_signal", "agent": "RiskManagement", "action": "adjust_capital", "message": alert['message'], "timestamp": datetime.now()})

        except Exception as e:
            logger.error(f"[ERROR] Feedback loop to agents failed: {e}")

    async def _save_live_data(self):
        """Save processed live data to parquet files"""
        while self.is_running:
            try:
                # Save aggregated live data
                await self._save_aggregated_data()

                logger.info("[SAVE] Saving live data...")
                await asyncio.sleep(300)  # Save every 5 minutes

            except Exception as e:
                logger.error(f"[ERROR] Live data saving failed: {e}")

    async def _save_aggregated_data(self):
        """Save aggregated market data"""
        try:
            # This would save processed and aggregated data
            # For now, just log the activity
            logger.debug("[SAVE] Saving aggregated market data...")
            # Example: Save the current state of market_data_cache to parquet files
            # for timeframe, data_dict in self.market_data_cache.items():
            #     for key, df in data_dict.items():
            #         if isinstance(df, pl.DataFrame) and not df.is_empty():
            #             file_path = self.data_path / "processed_live" / timeframe / f"{key}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.parquet"
            #             file_path.parent.mkdir(parents=True, exist_ok=True)
            #             df.write_parquet(file_path)
            #             logger.debug(f"Saved processed data for {key} {timeframe} to {file_path}")

        except Exception as e:
            logger.error(f"[ERROR] Failed to save aggregated data: {e}")
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            logger.info("[CLEANUP] Cleaning up Options Market Monitoring Agent...")
            self.is_running = False
            logger.info("[SUCCESS] Options Market Monitoring Agent cleaned up")
        except Exception as e:
            logger.error(f"[ERROR] Cleanup failed: {e}")

# Example usage
async def main():
    print("Starting Options Market Monitoring Agent...")
    agent = OptionsMarketMonitoringAgent()
    try:
        print("Initializing agent...")
        success = await agent.initialize()
        if success:
            print("Agent initialized successfully. Starting monitoring...")
            await agent.start()
        else:
            print("Failed to initialize agent")
    except KeyboardInterrupt:
        print("Agent interrupted by user")
        logger.info("Agent interrupted by user")
    except Exception as e:
        print(f"Error in main: {e}")
        logger.error(f"Error in main: {e}")
    finally:
        print("Cleaning up...")
        await agent.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
