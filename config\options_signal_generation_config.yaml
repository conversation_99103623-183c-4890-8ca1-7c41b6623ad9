# 🎯 Options Signal Generation Agent Configuration
# Real-time signal generation for NIFTY & BANKNIFTY options trading

# Core Settings
signal_generation:
  # Signal Types
  signal_types: ["volatility", "directional", "flow", "greeks"]
  
  # Timeframes for signal generation
  timeframes: ["1min", "3min", "5min", "15min"]
  
  # Underlying symbols
  underlying_symbols: ["NIFTY", "BANKNIFTY"]
  
  # Signal quality thresholds
  confidence_threshold: 0.7
  min_signal_strength: 0.6
  
  # Timing settings
  signal_interval: 30  # seconds between signal generation cycles
  continuous_mode: true  # Enable continuous signal generation
  run_once: false  # Disable single-run mode for live trading
  
  # Data processing
  chunk_size: 50000
  max_concurrent_tasks: 20  # Increased to handle all strategies
  use_lazy_loading: true
  batch_processing: true

# AI Model Configuration
ai_models:
  model_inference_enabled: true
  models_path: "data/models"
  fallback_confidence: 0.5
  
  # Model types
  volatility_model: "volatility_predictor_v1.pkl"
  directional_model: "directional_predictor_v1.pkl"
  flow_model: "flow_predictor_v1.pkl"
  greeks_model: "greeks_predictor_v1.pkl"

# Data Sources
data_sources:
  live_data_path: "data/live"
  historical_data_path: "data/historical"
  indicators_data_path: "data/indicators"
  
  # Data validation
  validate_data: true
  min_data_points: 50
  max_data_age_minutes: 5

# Strategy Configuration
strategies:
  # Enable/disable strategy categories
  volatility_strategies: true
  directional_strategies: true
  flow_strategies: true
  greeks_strategies: true
  
  # Strategy selection
  max_strategies_per_cycle: 10
  strategy_rotation: true
  
# Market Regime Filtering
market_regime:
  enable_regime_filtering: true
  regime_confidence_threshold: 0.6
  
  # Regime types
  supported_regimes: ["trending", "ranging", "volatile", "calm"]
  
# Risk Management Integration
risk_management:
  enable_risk_filtering: true
  max_daily_risk_percent: 10.0
  max_trades_per_day: 20
  max_capital_at_risk_percent: 15.0
  
  # Position sizing
  default_lot_size: 1
  max_lot_size: 5
  
# Signal Output
output:
  save_signals: true
  signals_output_path: "data/signals"
  signal_format: "json"
  
  # Real-time broadcasting
  broadcast_signals: true
  signal_queue_size: 100
  
# Performance Monitoring
performance:
  track_signal_performance: true
  performance_window_days: 30
  min_trades_for_stats: 10
  
# Logging
logging:
  log_level: "INFO"
  log_signals: true
  log_performance: true
  log_errors: true

# Advanced Features
advanced:
  # Multi-timeframe analysis
  multi_timeframe_analysis: true
  timeframe_weights:
    "1min": 0.2
    "3min": 0.3
    "5min": 0.3
    "15min": 0.2
  
  # Signal correlation
  check_signal_correlation: true
  max_correlation_threshold: 0.8
  
  # Dynamic adjustments
  dynamic_confidence_adjustment: true
  market_condition_adjustment: true
